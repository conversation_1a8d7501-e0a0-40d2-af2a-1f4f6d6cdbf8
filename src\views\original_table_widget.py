#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
原始记录表UI组件
现已集成列级别公式计算功能
"""

import os
import json
import logging
import pandas as pd
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QComboBox, QLineEdit, QTextEdit, QDateEdit, QPushButton,
    QToolBar, QAction, QMenu, QMessageBox, QCheckBox, QFrame, QAbstractItemView,
    QSizePolicy, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QEvent
from PyQt5.QtGui import QIcon

from src.models.original import Original
from src.utils.resource_loader import ResourceLoader
from src.views.base_table_widget import BaseTableWidget
from src.formula.formula_config_manager import FormulaConfigManager

class OriginalTableWidget(BaseTableWidget):
    """
    原始记录表组件

    继承自BaseTableWidget，自动获得列公式计算功能：
    1. 列公式设置和管理
    2. 自动计算和更新
    3. 公式配置保存和加载
    4. 统一的用户交互界面
    """

    def __init__(self, parent=None):
        # 先调用父类初始化，传入表格名称
        super().__init__(table_name="原始记录表", parent=parent)

        # 启用公式功能，支持用户手动添加公式列
        self.formula_enabled = True

        # 初始化数据模型
        self.original_model = Original()
        self.data_model = self.original_model  # 设置基类的data_model

        # 初始化字段配置
        self.field_groups = self._load_field_groups()

        # 加载字段定义配置
        self.field_definitions = self._load_field_definitions()

        # 加载显示设置
        self.display_settings = self._load_display_settings()

        # 初始化选择处理器
        self._init_selection_handlers()

        # 加载数据
        self.load_data()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建工具栏
        self._create_toolbar()
        layout.addWidget(self.toolbar)
        
        # 创建表格
        self._create_table()
        layout.addWidget(self.table)
    
    def _create_toolbar(self):
        """创建工具栏"""
        self.toolbar = QToolBar("原始记录表工具栏")
        self.toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # 新增记录
        self.add_action = QAction(ResourceLoader.load_icon("add.png"), "新增记录", self)
        self.add_action.triggered.connect(self.add_record)
        self.toolbar.addAction(self.add_action)
        
        # 删除记录
        self.delete_action = QAction(ResourceLoader.load_icon("delete.png"), "删除记录", self)
        self.delete_action.triggered.connect(self.delete_record)
        self.toolbar.addAction(self.delete_action)
        
        self.toolbar.addSeparator()
        
        # 刷新数据
        self.refresh_action = QAction(ResourceLoader.load_icon("refresh.png"), "刷新", self)
        self.refresh_action.triggered.connect(self.load_data)
        self.toolbar.addAction(self.refresh_action)
        
        # 导出数据
        self.export_action = QAction(ResourceLoader.load_icon("export.png"), "导出", self)
        self.export_action.triggered.connect(self.export_data)
        self.toolbar.addAction(self.export_action)
    
    def _create_table(self):
        """创建表格"""
        self.table = QTableWidget()
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.setSortingEnabled(False)

        # 设置表头
        self.table.horizontalHeader().setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter | Qt.TextWordWrap)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.horizontalHeader().setMinimumSectionSize(60)

        # 设置垂直表头可见，便于行操作
        self.table.verticalHeader().setVisible(True)
        self.table.verticalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # 设置选择模式 - 改为支持多选和单元格选择
        self.table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.table.setSelectionBehavior(QAbstractItemView.SelectItems)

        # 设置编辑触发器：支持双击和键盘输入编辑
        self.table.setEditTriggers(QAbstractItemView.DoubleClicked |
                                  QAbstractItemView.EditKeyPressed)

        # 连接信号（先断开可能存在的连接，避免重复连接）
        try:
            self.table.customContextMenuRequested.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        try:
            self.table.cellChanged.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        try:
            self.table.itemSelectionChanged.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        try:
            self.table.cellClicked.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        # 重新连接所有必要的信号
        self.table.customContextMenuRequested.connect(self._show_context_menu)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.cellChanged.connect(self._on_cell_changed)
        self.table.cellClicked.connect(self._on_cell_clicked)

        # 注意：不在这里安装事件过滤器，而是在_init_selection_handlers之后安装

    def _init_selection_handlers(self):
        """初始化选择处理器"""
        try:
            # 导入表格选择处理器以支持多选、复制、粘贴和删除功能
            from src.utils.table_selection_handler import TableSelectionHandler
            self.table_selection_handler = TableSelectionHandler(self.table)

            # 导入多单元格选择管理器以增强Ctrl和Shift多选功能
            from src.utils.multi_cell_selection_manager import MultiCellSelectionManager
            self.multi_cell_selection_manager = MultiCellSelectionManager(self.table)

            # 导入行插入管理器以支持行插入功能
            from src.utils.row_insert_manager import RowInsertManager
            self.row_insert_manager = RowInsertManager(self.table, self)

            # 连接多选信号
            self._connect_multi_selection_signals()

            self.logger.info("选择处理器初始化完成")

            # 最后安装键盘事件过滤器以处理键盘事件（确保优先级最高）
            self.table.installEventFilter(self)
            self.logger.info("键盘事件过滤器已安装")

        except Exception as e:
            self.logger.error(f"初始化选择处理器失败: {e}")

    def _connect_multi_selection_signals(self):
        """连接多选相关信号"""
        try:
            if hasattr(self, 'multi_cell_selection_manager'):
                # 连接Ctrl选择信号
                if hasattr(self.multi_cell_selection_manager, 'ctrl_selection'):
                    self.multi_cell_selection_manager.ctrl_selection.connect(self._on_ctrl_selection)
                # 连接Shift选择信号
                if hasattr(self.multi_cell_selection_manager, 'shift_selection'):
                    self.multi_cell_selection_manager.shift_selection.connect(self._on_shift_selection)
                # 连接范围选择信号（如果存在）
                if hasattr(self.multi_cell_selection_manager, 'range_selection'):
                    self.multi_cell_selection_manager.range_selection.connect(self._on_range_selection)

        except Exception as e:
            self.logger.error(f"连接多选信号失败: {e}")

    def _on_ctrl_selection(self, row, col, selected_cells):
        """处理Ctrl多选"""
        self.logger.info(f"Ctrl多选: 点击({row}, {col}), 当前选中{len(selected_cells)}个单元格")

    def _on_shift_selection(self, start_row, start_col, end_row, end_col, selected_cells):
        """处理Shift范围选择"""
        self.logger.info(f"Shift范围选择: 从({start_row}, {start_col})到({end_row}, {end_col}), 选中{len(selected_cells)}个单元格")

    def _on_range_selection(self, start_row, start_col, end_row, end_col):
        """处理范围选择"""
        self.logger.info(f"范围选择: 从({start_row}, {start_col})到({end_row}, {end_col})")

    def eventFilter(self, source, event):
        """事件过滤器，用于处理表格的键盘编辑事件"""
        from PyQt5.QtCore import QEvent, Qt
        from PyQt5.QtWidgets import QApplication

        # 处理表格的键盘事件
        if source == self.table and event.type() == QEvent.KeyPress:
            # 检查是否是需要特殊处理的快捷键，让其他处理器优先处理
            if event.modifiers() == Qt.ControlModifier:
                if event.key() in (Qt.Key_C, Qt.Key_V, Qt.Key_A, Qt.Key_X):
                    # 这些快捷键应该由TableSelectionHandler处理，不要拦截
                    return super().eventFilter(source, event)
            elif event.key() == Qt.Key_Delete:
                # Delete键也应该由TableSelectionHandler处理
                return super().eventFilter(source, event)

            # 处理键盘输入和编辑事件
            handled = self._handle_table_key_press(event)
            if handled:
                return True

        # 处理输入法事件（中文输入）
        elif source == self.table and event.type() == QEvent.InputMethod:
            self.logger.debug(f"输入法事件: {event}")
            handled = self._handle_input_method_event(event)
            if handled:
                return True

        return super().eventFilter(source, event)

    def _handle_input_method_event(self, event):
        """处理输入法事件（中文输入）"""
        try:
            # 获取当前选中的单元格
            current_row = self.table.currentRow()
            current_col = self.table.currentColumn()

            if current_row < 0 or current_col < 0:
                return False

            # 获取输入法提交的文本
            commit_string = event.commitString()
            self.logger.debug(f"输入法提交文本: '{commit_string}', 长度={len(commit_string)}")

            if commit_string:
                # 检查是否有自定义控件
                widget = self.table.cellWidget(current_row, current_col)
                if widget:
                    return False

                # 检查普通单元格是否可编辑
                current_item = self.table.item(current_row, current_col)
                if current_item and (current_item.flags() & Qt.ItemIsEditable):
                    self.logger.debug(f"✅ 输入法文本通过检测: '{commit_string}'")

                    # 记录编辑前的值
                    self._cell_edit_old_value = current_item.text()

                    # 直接设置单元格内容为输入文字
                    current_item.setText(commit_string)

                    # 进入编辑模式
                    self.table.editItem(current_item)

                    # 设置光标位置
                    from PyQt5.QtCore import QTimer
                    from PyQt5.QtWidgets import QApplication

                    def fix_cursor_position():
                        focus_widget = QApplication.focusWidget()
                        if focus_widget and hasattr(focus_widget, 'setText') and hasattr(focus_widget, 'setCursorPosition'):
                            focus_widget.setText(commit_string)
                            focus_widget.setCursorPosition(len(commit_string))
                            if hasattr(focus_widget, 'deselect'):
                                focus_widget.deselect()
                            self.logger.debug(f"输入法设置编辑器: '{commit_string}', 光标位置: {len(commit_string)}")

                    QTimer.singleShot(10, fix_cursor_position)
                    return True

        except Exception as e:
            self.logger.error(f"处理输入法事件失败: {e}")

        return False

    def _should_trigger_edit_mode(self, event):
        """
        判断按键事件是否应该触发编辑模式

        参数:
            event: QKeyEvent对象

        返回:
            bool: True表示应该触发编辑模式，False表示不应该
        """
        # 获取按键代码和输入文本
        key = event.key()
        input_text = event.text()
        modifiers = event.modifiers()

        # 1. 明确排除不应该触发编辑的按键
        non_edit_keys = {
            Qt.Key_Up, Qt.Key_Down, Qt.Key_Left, Qt.Key_Right,  # 方向键
            Qt.Key_Alt, Qt.Key_Shift, Qt.Key_Control, Qt.Key_Meta,  # 修饰键
            Qt.Key_F1, Qt.Key_F2, Qt.Key_F3, Qt.Key_F4, Qt.Key_F5, Qt.Key_F6,  # 功能键
            Qt.Key_F7, Qt.Key_F8, Qt.Key_F9, Qt.Key_F10, Qt.Key_F11, Qt.Key_F12,
            Qt.Key_Escape, Qt.Key_Tab, Qt.Key_Backtab,  # 特殊控制键
            Qt.Key_Insert, Qt.Key_Delete, Qt.Key_Home, Qt.Key_End,
            Qt.Key_PageUp, Qt.Key_PageDown, Qt.Key_CapsLock,
            Qt.Key_NumLock, Qt.Key_ScrollLock, Qt.Key_Pause, Qt.Key_Print
        }

        if key in non_edit_keys:
            # 取消按键类通知日志打印
            return False

        # 2. 排除带有Ctrl或Alt修饰键的组合键（这些通常是快捷键）
        if modifiers & (Qt.ControlModifier | Qt.AltModifier):
            # 取消按键类通知日志打印
            return False

        # 3. 检查是否有有效的输入文本
        if not input_text or len(input_text) == 0:
            # 取消按键类通知日志打印
            return False

        # 4. 检查是否是可打印字符或有效的输入字符
        for char in input_text:
            # 支持可打印的ASCII字符
            if char.isprintable():
                # 取消按键类通知日志打印
                return True
            # 支持中文和其他Unicode字符（码点大于127）
            if ord(char) > 127:
                # 取消按键类通知日志打印
                return True
            # 支持字母数字字符
            if char.isalnum():
                # 取消按键类通知日志打印
                return True

        # 取消按键类通知日志打印
        return False

    def _handle_table_key_press(self, event):
        """处理表格的键盘按下事件"""
        # 获取当前选中的单元格
        current_row = self.table.currentRow()
        current_col = self.table.currentColumn()

        if current_row < 0 or current_col < 0:
            return False

        # 检查是否是Esc键
        if event.key() == Qt.Key_Escape:
            # 如果当前单元格正在编辑，取消编辑
            if self.table.state() == QAbstractItemView.EditingState:
                current_item = self.table.currentItem()
                if current_item:
                    # 恢复原值
                    if hasattr(self, '_cell_edit_old_value'):
                        current_item.setText(self._cell_edit_old_value)
                        delattr(self, '_cell_edit_old_value')
                    # 结束编辑
                    self.table.closePersistentEditor(current_item)
                return True
            return False

        # 检查是否是Enter或Return键
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            self.logger.debug(f"Enter键被按下，当前编辑状态: {self.table.state()}")

            # 如果当前单元格正在编辑，完成编辑并保存数据
            if self.table.state() == QAbstractItemView.EditingState:
                current_item = self.table.currentItem()
                if current_item:
                    self.logger.debug(f"准备保存编辑内容，行: {current_row}, 列: {current_col}")

                    # 获取编辑器中的当前内容 - 使用更可靠的方法
                    editor_content = None

                    # 方法1: 尝试通过QApplication获取焦点控件
                    from PyQt5.QtWidgets import QApplication
                    focus_widget = QApplication.focusWidget()
                    if focus_widget and hasattr(focus_widget, 'text'):
                        editor_content = focus_widget.text()
                        self.logger.debug(f"通过焦点控件获取编辑器内容: '{editor_content}'")

                    # 方法2: 如果方法1失败，尝试遍历所有子控件
                    if editor_content is None:
                        def find_editor(widget):
                            for child in widget.children():
                                if (hasattr(child, 'text') and hasattr(child, 'setText') and
                                    child.isVisible() and hasattr(child, 'hasFocus') and child.hasFocus()):
                                    return child.text()
                                # 递归查找
                                result = find_editor(child)
                                if result is not None:
                                    return result
                            return None

                        editor_content = find_editor(self.table)
                        if editor_content is not None:
                            self.logger.debug(f"通过递归查找获取编辑器内容: '{editor_content}'")

                    # 方法3: 如果还是没找到，尝试获取当前item的文本（可能已经更新了）
                    if editor_content is None:
                        editor_content = current_item.text()
                        self.logger.debug(f"使用当前item文本作为编辑器内容: '{editor_content}'")

                    # 如果找到编辑器内容，先更新到item
                    if editor_content is not None and editor_content != current_item.text():
                        current_item.setText(editor_content)
                        self.logger.debug(f"更新单元格内容为: '{editor_content}'")

                    # 记录编辑前后的值
                    old_value = getattr(self, '_cell_edit_old_value', '')
                    new_value = current_item.text()
                    self.logger.debug(f"编辑前值: '{old_value}', 编辑后值: '{new_value}'")

                    # 结束编辑，这会自动触发cellChanged信号保存数据
                    self.table.closePersistentEditor(current_item)
                    self.logger.debug("已调用 closePersistentEditor")

                    # 清理编辑状态
                    if hasattr(self, '_cell_edit_old_value'):
                        delattr(self, '_cell_edit_old_value')

                    # 移动到下一行的同一列
                    if current_row < self.table.rowCount() - 1:
                        self.table.setCurrentCell(current_row + 1, current_col)
                        self.logger.debug(f"移动到下一行: {current_row + 1}")
                return True
            else:
                self.logger.debug("不在编辑状态，Enter键移动到下一行")
                # 如果没有在编辑状态，Enter键移动到下一行
                if current_row < self.table.rowCount() - 1:
                    self.table.setCurrentCell(current_row + 1, current_col)
                return True

        # 使用新的键盘事件过滤逻辑判断是否应该触发编辑模式
        if self._should_trigger_edit_mode(event):
            input_text = event.text()
            self.logger.debug(f"✅ 输入文本通过检测: '{input_text}'")

            # 检查是否有自定义控件
            widget = self.table.cellWidget(current_row, current_col)
            if widget:
                # 对于自定义控件，不处理键盘输入，让控件自己处理
                return False

            # 检查普通单元格是否可编辑
            current_item = self.table.item(current_row, current_col)
            if current_item and (current_item.flags() & Qt.ItemIsEditable):
                # 记录编辑前的值
                self._cell_edit_old_value = current_item.text()

                # 修复问题1：正确处理键盘输入进入编辑模式并保存第一个字符
                # 直接设置单元格内容为输入字符，然后进入编辑模式
                current_item.setText(input_text)

                # 进入编辑模式
                self.table.editItem(current_item)

                # 修复光标位置和文本选中问题
                from PyQt5.QtCore import QTimer
                from PyQt5.QtWidgets import QApplication

                def fix_cursor_position():
                    # 方法1: 通过QApplication获取焦点控件（最可靠）
                    focus_widget = QApplication.focusWidget()
                    if focus_widget and hasattr(focus_widget, 'setText') and hasattr(focus_widget, 'setCursorPosition'):
                        # 确保文本正确设置
                        focus_widget.setText(input_text)
                        # 将光标移到末尾，避免文本被选中
                        focus_widget.setCursorPosition(len(input_text))
                        # 取消文本选择
                        if hasattr(focus_widget, 'deselect'):
                            focus_widget.deselect()
                        self.logger.debug(f"通过焦点控件设置编辑器: '{input_text}', 光标位置: {len(input_text)}")
                        return

                    # 方法2: 递归查找编辑器（备用方案）
                    def find_editor(widget):
                        for child in widget.children():
                            if (hasattr(child, 'setText') and hasattr(child, 'text') and
                                hasattr(child, 'setCursorPosition') and child.isVisible()):
                                if hasattr(child, 'hasFocus') and child.hasFocus():
                                    return child
                            # 递归查找
                            result = find_editor(child)
                            if result:
                                return result
                        return None

                    editor = find_editor(self.table)
                    if editor:
                        editor.setText(input_text)
                        editor.setCursorPosition(len(input_text))
                        if hasattr(editor, 'deselect'):
                            editor.deselect()
                        self.logger.debug(f"通过递归查找设置编辑器: '{input_text}', 光标位置: {len(input_text)}")

                # 延迟执行以确保编辑器已创建
                QTimer.singleShot(10, fix_cursor_position)

                return True

        return False
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        try:
            # 加载单独列字段
            separate_fields_path = os.path.join("config", "原始表单独列字段.json")
            if os.path.exists(separate_fields_path):
                with open(separate_fields_path, 'r', encoding='utf-8') as f:
                    separate_data = json.load(f)
                    if separate_data and isinstance(separate_data, list) and len(separate_data) > 0:
                        separate_fields = list(separate_data[0].keys())
                    else:
                        separate_fields = []
            else:
                separate_fields = ["id", "样车编号", "问题简述", "问题描述_QTM_DTS_DTM", 
                                 "纬度", "经度", "测试软件版本", "问题日期_QTM_DTS_DTM", 
                                 "具体时间", "问题提出人", "问题有效性"]
            
            # 加载固定字段
            fixed_fields_path = os.path.join("config", "原始表固定字段.json")
            if os.path.exists(fixed_fields_path):
                with open(fixed_fields_path, 'r', encoding='utf-8') as f:
                    fixed_data = json.load(f)
                    if fixed_data and isinstance(fixed_data, list) and len(fixed_data) > 0:
                        fixed_fields = list(fixed_data[0].keys())
                    else:
                        fixed_fields = []
            else:
                fixed_fields = ["道路类型", "功能模式", "位置", "行车问题分类", "泊车问题分类", 
                              "接管类型", "日志数据批次", "dat数据批次", "dat数据", 
                              "严重度_QTM_DTS_DTM", "场景类型", "车速"]
            
            # 加载可变字段
            variable_fields_path = os.path.join("config", "原始表可变字段.json")
            if os.path.exists(variable_fields_path):
                with open(variable_fields_path, 'r', encoding='utf-8') as f:
                    variable_data = json.load(f)
                    if variable_data and isinstance(variable_data, list) and len(variable_data) > 0:
                        variable_fields = list(variable_data[0].keys())
                    else:
                        variable_fields = []
            else:
                variable_fields = ["数据来源", "测试环境", "天气条件", "路面状况", "交通状况", 
                                 "测试人员", "复现次数", "问题等级", "影响范围", "紧急程度", 
                                 "处理状态", "分配给", "预计完成时间", "实际完成时间", "验证结果", "备注"]
            
            return {
                "单独列字段": separate_fields,
                "固定字段": fixed_fields,
                "可变字段": variable_fields
            }
            
        except Exception as e:
            self.logger.error(f"加载字段分组配置失败: {e}")
            # 返回默认配置
            return {
                "单独列字段": ["id", "样车编号", "问题简述", "问题描述_QTM_DTS_DTM", 
                             "纬度", "经度", "测试软件版本", "问题日期_QTM_DTS_DTM", 
                             "具体时间", "问题提出人", "问题有效性"],
                "固定字段": ["道路类型", "功能模式", "位置", "行车问题分类", "泊车问题分类", 
                            "接管类型", "日志数据批次", "dat数据批次", "dat数据", 
                            "严重度_QTM_DTS_DTM", "场景类型", "车速"],
                "可变字段": ["数据来源", "测试环境", "天气条件", "路面状况", "交通状况", 
                           "测试人员", "复现次数", "问题等级", "影响范围", "紧急程度", 
                           "处理状态", "分配给", "预计完成时间", "实际完成时间", "验证结果", "备注"]
            }

    def _load_field_definitions(self):
        """加载字段定义配置"""
        try:
            # 加载字段定义配置文件
            config_path = os.path.join(os.getcwd(), 'config', 'original_table_field_definitions.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    definitions = json.load(f)
                    self.logger.info(f"成功加载原始记录表字段定义配置: {config_path}")
                    return definitions
            else:
                self.logger.warning(f"原始记录表字段定义配置文件不存在: {config_path}")

        except Exception as e:
            self.logger.error(f"加载原始记录表字段定义配置失败: {e}")

        return {}

    def _load_display_settings(self):
        """加载显示设置"""
        settings_path = os.path.join("config", "原始表显示设置.json")
        if os.path.exists(settings_path):
            try:
                with open(settings_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载显示设置失败: {e}")

        # 返回默认设置
        all_fields = (self.field_groups["单独列字段"] +
                     self.field_groups["固定字段"] +
                     self.field_groups["可变字段"])

        return {
            "visible_fields": all_fields,
            "field_order": all_fields
        }

    def apply_display_settings(self, settings):
        """应用显示设置"""
        try:
            self.display_settings = settings
            # 重新加载数据以应用新的显示设置
            self.load_data()
            self.logger.info("显示设置已应用")
        except Exception as e:
            self.logger.error(f"应用显示设置失败: {e}")

    def load_data(self):
        """
        加载数据并应用公式计算
        重写基类方法以支持公式功能
        """
        try:
            # 获取数据 - 使用original_model
            if hasattr(self, 'original_model') and hasattr(self.original_model, 'get_all_records'):
                df = self.original_model.get_all_records()
            else:
                df = pd.DataFrame()  # 如果没有数据模型，使用空数据框

            if df.empty:
                self.logger.info('没有原始记录表数据')
                self.data_frame = pd.DataFrame()
                # 清空表格显示
                self.table.setRowCount(0)
                self.table.setColumnCount(0)
                return

            # 先展开JSON字段，确保公式计算能访问到所有字段
            expanded_df = self._expand_json_fields(df)

            # 设置数据框
            self.data_frame = expanded_df.copy()

            # 如果有公式列，重新计算
            if hasattr(self, 'formula_enabled') and self.formula_enabled and hasattr(self, 'formula_columns') and self.formula_columns:
                if hasattr(self, 'formula_engine'):
                    self.data_frame = self.formula_engine.calculate_all_formula_columns(
                        self.table_name, self.data_frame
                    )

            # 设置表格显示 - 直接使用已处理的数据框
            self._setup_table_display_only(self.data_frame)

            # 加载公式配置
            self.load_formula_config()

            self.logger.info(f'成功加载 {len(df)} 条原始记录表记录')

        except Exception as e:
            self.logger.error(f'加载原始记录表数据失败: {e}')
            QMessageBox.critical(self, "错误", f"加载数据失败: {str(e)}")
    
    def _setup_empty_table(self):
        """设置空表格"""
        # 根据显示设置获取可见字段和顺序
        visible_fields = self.display_settings.get("visible_fields", [])
        field_order = self.display_settings.get("field_order", [])

        # 使用字段顺序，只显示可见字段
        ordered_visible_fields = [field for field in field_order if field in visible_fields]

        # 不添加复选框列，直接使用字段列
        headers = ordered_visible_fields

        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        self.table.setRowCount(0)

        # 隐藏不可见的列（如果有的话）
        self._apply_column_visibility(ordered_visible_fields)
    
    def _expand_json_fields(self, df):
        """展开JSON字段"""
        expanded_df = df.copy()

        try:
            # 首先确保所有固定字段和可变字段的列都存在
            # 从配置文件获取所有可能的字段
            all_fixed_fields = self.field_groups.get('固定字段', [])
            all_variable_fields = self.field_groups.get('可变字段', [])

            # 为所有固定字段和可变字段创建空列
            for field in all_fixed_fields + all_variable_fields:
                if field not in expanded_df.columns:
                    expanded_df[field] = None

            # 展开固定字段JSON
            if '原始表固定字段' in df.columns:
                for idx, row in df.iterrows():
                    fixed_json = row['原始表固定字段']
                    if fixed_json and fixed_json.strip():
                        try:
                            fixed_data = json.loads(fixed_json)
                            if isinstance(fixed_data, dict):
                                for key, value in fixed_data.items():
                                    if key in all_fixed_fields:  # 只处理配置中定义的字段
                                        expanded_df.at[idx, key] = value
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"解析固定字段JSON失败 (行{idx}): {e}")

            # 展开可变字段JSON
            if '原始表可变字段' in df.columns:
                for idx, row in df.iterrows():
                    variable_json = row['原始表可变字段']
                    if variable_json and variable_json.strip():
                        try:
                            variable_data = json.loads(variable_json)
                            if isinstance(variable_data, dict):
                                for key, value in variable_data.items():
                                    if key in all_variable_fields:  # 只处理配置中定义的字段
                                        expanded_df.at[idx, key] = value
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"解析可变字段JSON失败 (行{idx}): {e}")

            # 移除原始JSON列
            columns_to_drop = ['原始表固定字段', '原始表可变字段']
            expanded_df = expanded_df.drop(columns=[col for col in columns_to_drop if col in expanded_df.columns])

            self.logger.info(f"JSON字段展开完成，固定字段: {all_fixed_fields}, 可变字段: {all_variable_fields}")

        except Exception as e:
            self.logger.error(f"展开JSON字段失败: {e}")

        return expanded_df

    def _setup_table_with_data(self, df):
        """使用数据设置表格"""
        try:
            # 展开JSON字段
            df = self._expand_json_fields(df)

            # 调用显示方法
            self._setup_table_display_only(df)

        except Exception as e:
            self.logger.error(f"设置原始记录表显示失败: {e}")

    def _setup_table_display_only(self, df):
        """仅设置表格显示，不进行JSON字段展开"""
        try:
            # 根据显示设置获取可见字段和顺序
            visible_fields = self.display_settings.get("visible_fields", [])
            field_order = self.display_settings.get("field_order", [])

            # 使用字段顺序，只显示可见字段
            ordered_visible_fields = [field for field in field_order if field in visible_fields and field in df.columns]

            # 不添加复选框列，直接使用字段列
            headers = ordered_visible_fields

            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            self.table.setRowCount(len(df))

            # 填充数据
            for row_idx, (_, row_data) in enumerate(df.iterrows()):
                # 填充列数据
                for col_idx, field in enumerate(ordered_visible_fields):
                    value = row_data.get(field, "")

                    # 使用字段定义创建控件
                    widget = self._create_cell_widget(field, value)

                    if widget is not None:
                        # 使用特殊控件
                        self.table.setCellWidget(row_idx, col_idx, widget)
                    else:
                        # 使用默认的文本单元格
                        item = QTableWidgetItem(str(value) if value is not None else "")
                        self.table.setItem(row_idx, col_idx, item)

            # 应用列可见性设置
            self._apply_column_visibility(ordered_visible_fields)

            self.logger.info(f"原始记录表显示设置完成，共 {len(df)} 行 {len(ordered_visible_fields)} 列")

            # 🔧 **关键修复**：原始记录表显示设置完成后重新安装QComboBox事件过滤器
            # 这确保新创建的QComboBox控件能够正确处理多选事件
            if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
                self.table_selection_handler.force_reinstall_all_combobox_filters()
                self.logger.info(f"🔧 原始记录表显示完成，已重新安装QComboBox事件过滤器")

        except Exception as e:
            self.logger.error(f"设置原始记录表显示失败: {e}")

    def _apply_column_visibility(self, visible_fields):
        """应用列可见性设置"""
        try:
            # 所有可能的字段
            all_fields = (self.field_groups["单独列字段"] +
                         self.field_groups["固定字段"] +
                         self.field_groups["可变字段"])

            # 由于我们已经只创建了可见的列，这里主要是为了确保表格正确显示
            # 如果需要动态隐藏/显示列，可以在这里实现
            self.logger.info(f"应用列可见性设置，显示 {len(visible_fields)} 个字段")

        except Exception as e:
            self.logger.error(f"应用列可见性设置失败: {e}")
    
    def add_record(self, batch_mode=False):
        """
        添加新记录 - 性能优化版本

        参数:
            batch_mode: 是否为批量模式，批量模式下不会立即重新加载表格
        """
        try:
            # 创建默认数据
            default_data = {
                "样车编号": "",
                "问题简述": "",
                "问题描述_QTM_DTS_DTM": "",
                "纬度": "",
                "经度": "",
                "测试软件版本": "",
                "问题日期_QTM_DTS_DTM": "",
                "具体时间": "",
                "问题提出人": "",
                "问题有效性": ""
            }

            # 添加记录
            record_id = self.original_model.add_record(default_data, field_groups=self.field_groups)

            if record_id:
                # 性能优化：批量模式下不重新加载表格，由调用方统一处理
                if not batch_mode:
                    # 增量添加行而不是重新加载整个表格
                    self._add_table_row(record_id, default_data)

                self.record_added.emit(record_id)

                # 减少日志输出频率
                if record_id % 10 == 0:  # 每10条记录记录一次日志
                    self.logger.info(f"添加原始记录成功，当前ID: {record_id}")

                return record_id
            else:
                if not batch_mode:
                    QMessageBox.warning(self, "警告", "添加记录失败")
                return None

        except Exception as e:
            self.logger.error(f"添加原始记录失败: {e}")
            if not batch_mode:
                QMessageBox.critical(self, "错误", f"添加记录失败: {e}")
            return None

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量

        返回:
            成功添加的记录ID列表
        """
        try:
            self.logger.info(f"开始批量添加 {count} 条原始记录")

            # 临时禁用表格信号，避免在批量操作期间触发单元格更新事件
            original_signals_blocked = self.table.signalsBlocked()
            self.table.blockSignals(True)

            # 设置批量操作标志
            self._updating_display = True

            added_record_ids = []

            try:
                # 批量添加记录
                for i in range(count):
                    record_id = self.add_record(batch_mode=True)
                    if record_id:
                        added_record_ids.append(record_id)
                    else:
                        self.logger.warning(f"批量添加第 {i+1} 条原始记录失败")

                # 批量操作完成后，统一重新加载表格
                if added_record_ids:
                    self.load_data()
                    self.logger.info(f"批量添加完成，成功添加 {len(added_record_ids)} 条原始记录")

                    # 发射批量添加完成信号
                    for record_id in added_record_ids:
                        self.record_added.emit(record_id)

                return added_record_ids

            finally:
                # 恢复表格信号和标志
                self._updating_display = False
                self.table.blockSignals(original_signals_blocked)

        except Exception as e:
            self.logger.error(f"批量添加原始记录失败: {e}")
            # 确保在异常情况下也恢复信号状态
            self._updating_display = False
            self.table.blockSignals(original_signals_blocked)
            QMessageBox.critical(self, "错误", f"批量添加记录失败: {e}")
            return []

    def _add_table_row(self, record_id, data):
        """增量添加表格行"""
        try:
            # 获取当前行数
            current_row_count = self.table.rowCount()

            # 添加新行
            self.table.insertRow(current_row_count)

            # 获取可见字段
            visible_fields = self.display_settings.get("visible_fields", [])
            field_order = self.display_settings.get("field_order", [])
            ordered_visible_fields = [field for field in field_order if field in visible_fields]

            # 填充数据
            for col_idx, field in enumerate(ordered_visible_fields):
                if field == "id":
                    value = record_id
                else:
                    value = data.get(field, "")

                # 创建控件或文本项
                widget = self._create_cell_widget(field, value)
                if widget is not None:
                    self.table.setCellWidget(current_row_count, col_idx, widget)
                else:
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    if field == "id":
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # ID列不可编辑
                    self.table.setItem(current_row_count, col_idx, item)

        except Exception as e:
            self.logger.error(f"增量添加表格行失败: {e}")
            # 如果增量添加失败，回退到完全重新加载
            self.load_data()

    def _create_checkbox_cell_widget(self, checked=False):
        """创建复选框单元格控件 - 只显示复选框，不显示数字"""
        # 创建容器widget
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setAlignment(Qt.AlignCenter)

        # 创建复选框
        checkbox = QCheckBox()
        checkbox.setChecked(checked)

        # 🔧 关键修复：设置复选框样式，确保不显示文本或数字
        checkbox.setText("")  # 确保没有文本
        checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 0px;
                margin: 0px;
                padding: 0px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
        """)

        layout.addWidget(checkbox)
        return widget

    def _populate_row_data(self, row, record_id, data):
        """填充行数据"""
        try:
            # 根据显示设置获取可见字段和顺序
            visible_fields = self.display_settings.get("visible_fields", [])
            field_order = self.display_settings.get("field_order", [])

            # 使用字段顺序，只显示可见字段
            ordered_visible_fields = [field for field in field_order if field in visible_fields]

            # 填充数据
            for col, field in enumerate(ordered_visible_fields):
                col_index = col  # 直接使用列索引
                if field == "id":
                    # ID列
                    item = QTableWidgetItem(str(record_id))
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # 设置ID列不可编辑
                    self.table.setItem(row, col_index, item)
                else:
                    # 其他列 - 使用字段定义创建控件
                    value = data.get(field, "")
                    widget = self._create_cell_widget(field, value)

                    if widget is not None:
                        # 使用特殊控件
                        self.table.setCellWidget(row, col_index, widget)
                    else:
                        # 使用默认的文本单元格
                        item = QTableWidgetItem(str(value))
                        self.table.setItem(row, col_index, item)

        except Exception as e:
            self.logger.error(f"填充行数据失败: {e}")

    def _create_cell_widget(self, field_name, value):
        """创建单元格控件"""
        # 根据字段定义配置创建不同的控件
        field_def = self.field_definitions.get(field_name, {})
        field_type = field_def.get('type', 'QLineEdit')
        field_options = field_def.get('options', [])

        # 根据字段类型创建控件
        if field_type == 'QComboBox' and field_options:

            # 🔧 统一QComboBox样式：参考试验问题表的标准样式
            # 直接创建QComboBox，不使用容器，避免布局异常
            combo = QComboBox()
            valid_options = [opt for opt in field_options if opt and str(opt).strip()]
            # 🔧 关键修复：添加空选项，与其他表格保持一致，允许用户清空选择
            combo.addItem("")  # 添加空选项作为第一项
            combo.addItems(valid_options)
            combo.setEditable(True)  # 🔧 关键修复：设置为可编辑，支持复制粘贴功能
            combo.setInsertPolicy(QComboBox.NoInsert)  # 🔧 关键修复：禁止插入新项，保持下拉选项不变

            # 🔧 关键修复：设置QComboBox的大小策略和约束，防止布局异常
            # 使用Expanding策略允许控件在水平方向上扩展和收缩，但保持在单元格内
            combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            # 🔧 设置最小和最大宽度，确保控件不会过度缩小或扩展
            combo.setMinimumWidth(80)  # 增加最小宽度，确保内容可见
            combo.setMaximumWidth(300)  # 设置最大宽度，防止过度扩展

            # 🔧 设置高度自适应，与普通文本字段保持一致
            # 不设置固定高度或最大高度限制，让QComboBox完全自适应表格行高
            combo.setMinimumHeight(20)  # 设置较小的最小高度，确保基本可见性
            # 移除最大高度限制，让QComboBox能够随行高自适应调节
            combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 垂直方向也使用Expanding策略

            # 🔧 设置统一样式，确保在小宽度下也能正常显示，移除外层容器框
            combo.setStyleSheet("""
                QComboBox {
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    padding: 2px 5px;
                    background-color: white;
                    min-width: 80px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    width: 12px;
                    height: 12px;
                }
            """)

            # 设置当前值
            if value:
                combo.setCurrentText(str(value))
            else:
                combo.setCurrentIndex(-1)  # 🔧 关键修复：空值时设置为无选择状态，允许用户清空

            return combo

        elif field_type == 'QTextEdit':
            from PyQt5.QtWidgets import QTextEdit
            text_edit = QTextEdit()
            text_edit.setPlainText(str(value) if value else "")
            text_edit.setMaximumHeight(60)
            return text_edit

        elif field_type == 'QDateEdit':
            from PyQt5.QtWidgets import QDateEdit
            from PyQt5.QtCore import QDate
            date_edit = QDateEdit()
            if value:
                try:
                    date = QDate.fromString(str(value), "yyyy-MM-dd")
                    if date.isValid():
                        date_edit.setDate(date)
                except:
                    pass
            return date_edit

        else:
            # 默认使用QTableWidgetItem (QLineEdit类型)
            return None  # 返回None表示使用默认的文本单元格

    def delete_record(self):
        """删除选中的记录 - 支持多行删除"""
        try:
            # 获取选中的行（通过单元格选择）
            selected_rows = set()
            for item in self.table.selectedItems():
                selected_rows.add(item.row())

            # 如果没有通过单元格选择，尝试获取选中的记录
            if not selected_rows:
                selected_ids = self.get_selected_records()
                if selected_ids:
                    # 根据ID找到对应的行
                    for row in range(self.table.rowCount()):
                        id_item = self.table.item(row, 0)  # ID在第0列（索引0）
                        if id_item and int(id_item.text()) in selected_ids:
                            selected_rows.add(row)

            # 如果仍然没有选中的行，使用当前行
            if not selected_rows:
                current_row = self.table.currentRow()
                if current_row >= 0:
                    selected_rows.add(current_row)

            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要删除的记录")
                return

            # 获取要删除的记录ID
            records_to_delete = []
            for row in selected_rows:
                id_item = self.table.item(row, 1)  # ID在第1列（索引1）
                if id_item:
                    record_id = int(id_item.text())
                    records_to_delete.append((row, record_id))

            if not records_to_delete:
                QMessageBox.warning(self, "警告", "无法获取要删除的记录ID")
                return

            # 确认删除
            if len(records_to_delete) == 1:
                record_id = records_to_delete[0][1]
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"确定要删除记录 ID: {record_id} 吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
            else:
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"确定要删除选中的 {len(records_to_delete)} 条记录吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

            if reply == QMessageBox.Yes:
                success_count = 0
                failed_count = 0

                # 按行号倒序删除，避免索引变化问题
                records_to_delete.sort(key=lambda x: x[0], reverse=True)

                for row, record_id in records_to_delete:
                    try:
                        if self.original_model.delete_record(record_id):
                            success_count += 1
                            self.record_deleted.emit(record_id)
                            # 减少日志输出
                            if record_id % 10 == 0:  # 每10条记录记录一次日志
                                self.logger.info(f"成功删除记录，ID: {record_id}")
                        else:
                            failed_count += 1
                            self.logger.warning(f"删除记录失败，ID: {record_id}")
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"删除记录异常，ID: {record_id}, 错误: {e}")

                # 重新加载数据以确保界面同步
                self.load_data()

                # 显示删除结果
                if success_count > 0:
                    if failed_count == 0:
                        QMessageBox.information(self, "成功", f"成功删除 {success_count} 条记录")
                    else:
                        QMessageBox.warning(self, "部分成功",
                                          f"成功删除 {success_count} 条记录，失败 {failed_count} 条记录")
                else:
                    QMessageBox.warning(self, "失败", "删除记录失败")

        except Exception as e:
            self.logger.error(f"删除记录失败: {e}")
            QMessageBox.critical(self, "错误", f"删除记录失败: {str(e)}")
    
    def export_data(self):
        """导出数据"""
        # TODO: 实现数据导出功能
        QMessageBox.information(self, "提示", "导出功能待实现")
    
    def _show_context_menu(self, position):
        """显示增强的右键菜单，与试验状态确认表和行驶记录表保持一致"""
        menu = QMenu(self)

        # 创建 "新增" 的级联菜单
        add_menu = menu.addMenu("新增")
        add_blank_row_action = add_menu.addAction("新增空白行")

        # 添加行插入功能
        insert_above_action = menu.addAction("向上方插入行")
        insert_below_action = menu.addAction("向下方插入行")

        menu.addSeparator()

        # 复制、粘贴、删除操作
        copy_action = menu.addAction("复制")
        paste_action = menu.addAction("粘贴")
        paste_as_new_action = menu.addAction("粘贴为新行")
        delete_action = menu.addAction("删除")

        menu.addSeparator()

        # 创建 "导出" 的级联菜单
        export_menu = menu.addMenu("导出")
        export_table_action = export_menu.addAction("导出表格")

        # 刷新
        refresh_action = menu.addAction("刷新")

        # 检查是否有选中的单元格或行
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0

        # 根据选择状态启用/禁用相关操作
        copy_action.setEnabled(has_selection)
        delete_action.setEnabled(has_selection)

        # 检查剪贴板是否有内容
        clipboard = QApplication.clipboard()
        has_clipboard_data = clipboard.text().strip() != ""
        paste_action.setEnabled(has_clipboard_data and has_selection)
        paste_as_new_action.setEnabled(has_clipboard_data)

        # 显示菜单并获取所选操作
        action = menu.exec_(self.table.mapToGlobal(position))

        # 处理菜单操作
        if action == add_blank_row_action:
            self.add_record()  # 调用正确的add_record方法，会在数据库中创建记录并生成ID
        elif action == insert_above_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_above()
        elif action == insert_below_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_below()
        elif action == copy_action:
            self._copy_selection()
        elif action == paste_action:
            self._paste_selection()
        elif action == paste_as_new_action:
            self._paste_as_new_row()
        elif action == delete_action:
            self.delete_record()
        elif action == export_table_action:
            self._export_data()
        elif action == refresh_action:
            self.load_data()

    def insert_rows_above(self):
        """向上方插入行"""
        if hasattr(self, 'row_insert_manager'):
            self.row_insert_manager.insert_rows_above()
        else:
            self.logger.error("行插入管理器未初始化")
            QMessageBox.warning(self, "错误", "行插入功能未就绪")

    def insert_rows_below(self):
        """向下方插入行"""
        if hasattr(self, 'row_insert_manager'):
            self.row_insert_manager.insert_rows_below()
        else:
            self.logger.error("行插入管理器未初始化")
            QMessageBox.warning(self, "错误", "行插入功能未就绪")

    def _insert_row_above(self):
        """向上方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _insert_row_below(self):
        """向下方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _copy_selection(self):
        """复制选中内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.copy_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    def _paste_selection(self):
        """粘贴内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.paste_to_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    def _paste_as_new_row(self):
        """粘贴为新行"""
        # 这个功能需要根据具体需求实现
        QMessageBox.information(self, "提示", "粘贴为新行功能待实现")
        self.logger.info("粘贴为新行功能被调用")

    def _export_data(self):
        """导出数据"""
        QMessageBox.information(self, "提示", "导出功能待实现")
        self.logger.info("导出功能被调用")

    def _on_cell_clicked(self, row, col):
        """处理单元格点击事件"""
        try:
            self.logger.info(f"单元格点击: 行={row}, 列={col}")

            # 如果当前有单元格正在编辑，先保存编辑内容
            if self.table.state() == QAbstractItemView.EditingState:
                current_item = self.table.currentItem()
                if current_item:
                    # 结束编辑，这会触发cellChanged信号自动保存
                    self.table.closePersistentEditor(current_item)

        except Exception as e:
            self.logger.error(f"处理单元格点击失败: {e}")

    def _on_selection_changed(self):
        """处理选择变化"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            # 获取记录ID
            id_item = self.table.item(current_row, 0)  # ID在第0列
            if id_item and id_item.text().strip():
                try:
                    record_id = int(id_item.text())
                    self.record_selected.emit(record_id)
                except ValueError:
                    # 如果ID不是有效的整数，跳过
                    pass
    
    def _on_cell_changed(self, row, column):
        """处理单元格变化"""
        try:
            # 防止在更新显示时触发数据保存
            if hasattr(self, '_updating_display') and self._updating_display:
                return

            # 🔧 新增：首先调用父类的撤销重做功能
            super()._on_cell_changed(row, column)

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 如果是公式列，不允许直接编辑，跳过保存
            if hasattr(self, 'formula_columns') and field_name in self.formula_columns:
                self.logger.debug(f"跳过公式列 {field_name} 的数据保存")
                return

            # 获取记录ID
            id_item = self.table.item(row, 1)  # ID在第1列
            if not id_item:
                return

            record_id_text = id_item.text()
            if not record_id_text or not record_id_text.isdigit():
                return

            record_id = int(record_id_text)

            new_value = self.table.item(row, column).text()

            # 构建更新数据
            data = {field_name: new_value}

            # 更新数据库
            if self.original_model.update_record(record_id, data, field_groups=self.field_groups):
                self.logger.debug(f"成功更新记录 {record_id} 的字段 {field_name}")
                self.record_updated.emit(record_id)

                # 🔧 改进：先同步单元格数据到数据框，然后触发公式重新计算
                self._sync_single_cell_to_dataframe(row, column, new_value)

                # 🔧 新增：触发公式重新计算
                if (hasattr(self, 'formula_enabled') and self.formula_enabled and
                    hasattr(self, 'formula_columns') and self.formula_columns):
                    self._trigger_formula_recalculation(field_name, row)

            else:
                self.logger.error(f"更新记录 {record_id} 的字段 {field_name} 失败")
                
        except Exception as e:
            self.logger.error(f"处理单元格变化失败: {e}")

    def _save_formula_results_to_database(self, formula_column: str, calculated_series: pd.Series):
        """将公式计算结果保存到数据库 - 优化版本，使用批量更新"""
        try:
            # 🔧 性能优化：收集所有需要更新的数据，进行批量更新
            batch_updates = []

            for row_index, value in calculated_series.items():
                record_id = self._get_record_id_for_row(row_index)
                if record_id:
                    batch_updates.append({
                        'id': record_id,
                        'data': {formula_column: value}
                    })

            if batch_updates:
                # 🔧 使用批量更新方法（如果数据模型支持）
                if hasattr(self.original_model, 'batch_update_records'):
                    success_count = self.original_model.batch_update_records(batch_updates, field_groups=self.field_groups)
                    self.logger.info(f"批量保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")
                else:
                    # 🔧 降级到逐条更新，但减少日志输出
                    success_count = 0
                    for update_item in batch_updates:
                        success = self.original_model.update_record(update_item['id'], update_item['data'], field_groups=self.field_groups)
                        if success:
                            success_count += 1

                    # 🔧 只输出汇总日志，避免大量DEBUG日志
                    self.logger.info(f"保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")

                    # 🔧 如果有失败的记录，输出警告
                    if success_count < len(batch_updates):
                        self.logger.warning(f"部分公式结果保存失败: {formula_column}列，失败{len(batch_updates) - success_count}条记录")

        except Exception as e:
            self.logger.error(f"保存原始记录公式结果到数据库失败: {e}")

    def _get_record_id_for_row(self, row_index: int):
        """获取指定行的记录ID"""
        try:
            # 原始记录表的ID在第一列
            id_item = self.table.item(row_index, 0)
            if id_item and id_item.text().isdigit():
                return int(id_item.text())
            return None
        except Exception as e:
            self.logger.error(f"获取原始记录行{row_index}的ID失败: {e}")
            return None

    # ==================== 公式功能相关方法 ====================

    def load_formula_config(self):
        """
        加载公式配置
        重写基类方法以实现具体的配置加载逻辑
        """
        try:
            if self.formula_config_manager.load_table_formulas(self.table_name, self.formula_engine):
                # 更新公式列标识
                column_formulas = self.formula_engine.get_column_formulas(self.table_name)
                self.formula_columns = set(column_formulas.keys())

                # 更新列头样式
                for column_name in self.formula_columns:
                    self._update_column_header_style(column_name, True)

                self.logger.info(f"成功加载{self.table_name}公式配置，共{len(self.formula_columns)}个公式列")
            else:
                self.logger.warning(f"加载{self.table_name}公式配置失败")

        except Exception as e:
            self.logger.error(f"加载{self.table_name}公式配置异常: {e}")

    def save_formula_config(self):
        """
        保存公式配置
        重写基类方法以实现具体的配置保存逻辑
        """
        try:
            if self.formula_config_manager.save_table_formulas(self.table_name, self.formula_engine):
                self.logger.info(f"成功保存{self.table_name}公式配置")
                return True
            else:
                self.logger.error(f"保存{self.table_name}公式配置失败")
                return False

        except Exception as e:
            self.logger.error(f"保存{self.table_name}公式配置异常: {e}")
            return False



    def get_formula_summary(self):
        """
        获取公式摘要信息
        """
        try:
            return self.formula_config_manager.get_table_formula_summary(self.table_name)
        except Exception as e:
            self.logger.error(f"获取{self.table_name}公式摘要失败: {e}")
            return {"table_name": self.table_name, "formula_count": 0, "formulas": []}

    def export_formula_config(self, export_file: str):
        """
        导出公式配置

        参数:
            export_file: 导出文件路径
        """
        try:
            return self.formula_config_manager.export_all_formulas(self.formula_engine, export_file)
        except Exception as e:
            self.logger.error(f"导出{self.table_name}公式配置失败: {e}")
            return False

    def import_formula_config(self, import_file: str):
        """
        导入公式配置

        参数:
            import_file: 导入文件路径
        """
        try:
            if self.formula_config_manager.import_all_formulas(self.formula_engine, import_file):
                # 重新加载数据以应用新公式
                self.load_data()
                return True
            return False
        except Exception as e:
            self.logger.error(f"导入{self.table_name}公式配置失败: {e}")
            return False

    def get_selected_records(self):
        """获取选中的记录ID列表 - 基于单元格选择"""
        selected_ids = []
        selected_rows = set()

        # 获取选中的单元格对应的行
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        # 获取这些行的ID
        for row in selected_rows:
            id_item = self.table.item(row, 0)  # ID在第0列
            if id_item and id_item.text().strip():
                try:
                    selected_ids.append(int(id_item.text()))
                except ValueError:
                    pass  # 跳过无效的ID

        return selected_ids

    def set_column_visibility(self, column_name, visible):
        """设置列的可见性"""
        for col in range(self.table.columnCount()):
            header_item = self.table.horizontalHeaderItem(col)
            if header_item and header_item.text() == column_name:
                self.table.setColumnHidden(col, not visible)
                break

    def get_column_order(self):
        """获取当前列的顺序"""
        order = []
        for col in range(self.table.columnCount()):
            header_item = self.table.horizontalHeaderItem(col)
            if header_item:
                order.append(header_item.text())
        return order

    def set_column_order(self, order):
        """设置列的顺序"""
        # TODO: 实现列顺序调整
        pass

    def reload_field_definitions(self):
        """重新加载字段定义配置"""
        try:
            self.field_definitions = self._load_field_definitions()
            self.logger.info("字段定义配置重新加载成功")
        except Exception as e:
            self.logger.error(f"重新加载字段定义配置失败: {e}")

    def import_field_json(self, json_data):
        """导入字段JSON配置"""
        try:
            # 重新加载字段分组配置
            self.field_groups = self._load_field_groups()

            # 重新加载字段定义配置
            self.reload_field_definitions()

            # 重新加载数据以应用新的字段配置
            self.load_data()
            self.logger.info("原始记录表字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入原始记录表字段JSON配置失败: {e}")
            raise
