#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
原始记录表管理器
负责管理原始记录表的所有功能，包括数据操作、UI管理、配置管理等
"""

import os
import json
import logging
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

from src.models.original import Original
from src.views.original_table_widget import OriginalTableWidget
from src.dialogs.original_display_settings_dialog import OriginalDisplaySettingsDialog

class OriginalTableManager(QObject):
    """原始记录表管理器"""
    
    # 信号定义
    status_message = pyqtSignal(str)  # 状态消息信号
    data_changed = pyqtSignal()       # 数据变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据模型
        self.original_model = Original()
        
        # 初始化UI组件
        self.original_widget = None
        
        # 初始化配置
        self.field_groups = self._load_field_groups()
        self.display_settings = self._load_display_settings()
    
    def create_widget(self):
        """创建原始记录表组件"""
        if self.original_widget is None:
            self.original_widget = OriginalTableWidget()
            
            # 连接信号
            self.original_widget.record_selected.connect(self._on_record_selected)
            self.original_widget.record_added.connect(self._on_record_added)
            self.original_widget.record_updated.connect(self._on_record_updated)
            self.original_widget.record_deleted.connect(self._on_record_deleted)
            
            self.logger.info("原始记录表组件创建成功")
        
        return self.original_widget
    
    def get_widget(self):
        """获取原始记录表组件"""
        if self.original_widget is None:
            return self.create_widget()
        return self.original_widget
    
    def refresh_data(self):
        """刷新数据"""
        try:
            if self.original_widget:
                self.original_widget.load_data()
                self.status_message.emit("原始记录表数据已刷新")
                self.data_changed.emit()
        except Exception as e:
            self.logger.error(f"刷新原始记录表数据失败: {e}")
            self.status_message.emit("刷新数据失败")
    
    def add_record(self, data=None):
        """添加新记录"""
        try:
            if self.original_widget:
                if data is None:
                    # 使用默认数据
                    self.original_widget.add_record()
                else:
                    # 使用提供的数据
                    record_id = self.original_model.add_record(data, field_groups=self.field_groups)
                    if record_id:
                        self.refresh_data()
                        self.status_message.emit(f"成功添加记录，ID: {record_id}")
                    else:
                        self.status_message.emit("添加记录失败")
        except Exception as e:
            self.logger.error(f"添加原始记录失败: {e}")
            self.status_message.emit("添加记录失败")

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量
        """
        try:
            if self.original_widget and hasattr(self.original_widget, 'add_multiple_records'):
                added_record_ids = self.original_widget.add_multiple_records(count)
                if added_record_ids:
                    self.status_message.emit(f"成功批量添加 {len(added_record_ids)} 条原始记录")
                    self.data_changed.emit()
                else:
                    self.status_message.emit("批量添加原始记录失败")
                return added_record_ids
            else:
                self.status_message.emit("不支持批量添加功能")
                return []
        except Exception as e:
            self.logger.error(f"批量添加原始记录失败: {e}")
            self.status_message.emit("批量添加记录失败")
            return []

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            if self.original_widget:
                selected_ids = self.original_widget.get_selected_records()
                if not selected_ids:
                    QMessageBox.information(self.parent, "提示", "请先选择要删除的记录")
                    return
                
                # 确认删除
                reply = QMessageBox.question(
                    self.parent, "确认删除", 
                    f"确定要删除选中的 {len(selected_ids)} 条记录吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    deleted_count = 0
                    for record_id in selected_ids:
                        if self.original_model.delete_record(record_id):
                            deleted_count += 1
                    
                    if deleted_count > 0:
                        self.refresh_data()
                        self.status_message.emit(f"成功删除 {deleted_count} 条记录")
                    else:
                        self.status_message.emit("删除记录失败")
        except Exception as e:
            self.logger.error(f"删除原始记录失败: {e}")
            self.status_message.emit("删除记录失败")
    
    def show_display_settings(self):
        """显示显示设置对话框"""
        try:
            dialog = OriginalDisplaySettingsDialog(self.parent)
            dialog.settings_changed.connect(self._on_display_settings_changed)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示原始记录表设置对话框失败: {e}")
            QMessageBox.critical(self.parent, "错误", f"显示设置对话框失败: {str(e)}")
    
    def import_field_json(self, json_data):
        """导入字段JSON配置"""
        try:
            if self.original_widget:
                self.original_widget.import_field_json(json_data)
                self.status_message.emit("字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入字段JSON配置失败: {e}")
            self.status_message.emit("导入字段JSON配置失败")

    def update_field_definitions(self, field_definitions):
        """更新字段定义"""
        try:
            if self.original_widget:
                # 重新加载字段分组配置
                self.field_groups = self._load_field_groups()
                # 更新widget的字段分组
                self.original_widget.field_groups = self.field_groups

                # 重新加载字段定义配置 - 修复：添加此行以确保字段定义配置文件被重新加载
                self.original_widget.reload_field_definitions()

                # 重新加载数据以应用新的字段配置
                self.original_widget.load_data()
                self.status_message.emit("原始记录表字段定义已更新")
                self.logger.info("原始记录表字段定义更新成功")
        except Exception as e:
            self.logger.error(f"更新原始记录表字段定义失败: {e}")
            self.status_message.emit("更新字段定义失败")
    
    def export_data(self, file_path=None):
        """导出数据"""
        try:
            # TODO: 实现数据导出功能
            QMessageBox.information(self.parent, "提示", "数据导出功能待实现")
        except Exception as e:
            self.logger.error(f"导出原始记录表数据失败: {e}")
            QMessageBox.critical(self.parent, "错误", f"导出数据失败: {str(e)}")
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        try:
            # 1. 优先尝试从新系统文件加载（字段导入功能生成的文件）
            config_path = os.path.join("config", "original_field_groups.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    new_groups = json.load(f)

                # 验证新系统文件是否与实际JSON文件同步
                actual_groups = self._extract_field_groups_from_json_files()
                if self._are_field_groups_consistent(new_groups, actual_groups):
                    self.logger.info("使用新系统字段分组文件（已验证与实际文件一致）")
                    return new_groups
                else:
                    self.logger.warning("新系统字段分组文件与实际JSON文件不一致，使用实际文件重建")
                    # 更新新系统文件以保持同步
                    try:
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(actual_groups, f, ensure_ascii=False, indent=4)
                        self.logger.info("已更新新系统字段分组文件以保持同步")
                    except Exception as e:
                        self.logger.error(f"更新新系统字段分组文件失败: {e}")
                    return actual_groups

            # 2. 兼容旧的配置文件路径
            old_config_path = os.path.join("config", "原始表字段分组.json")
            if os.path.exists(old_config_path):
                with open(old_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 3. 从旧系统文件构建字段分组（兼容性处理）
            actual_groups = self._extract_field_groups_from_json_files()
            if any(actual_groups.values()):
                # 保存为新系统文件以便下次使用
                try:
                    os.makedirs(os.path.dirname(config_path), exist_ok=True)
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(actual_groups, f, ensure_ascii=False, indent=4)
                    self.logger.info(f"从实际JSON文件构建字段分组并保存到 {config_path}")
                except Exception as e:
                    self.logger.error(f"保存字段分组文件失败: {e}")

                return actual_groups

            # 4. 如果都失败了，使用旧的逐个加载方式作为后备
            # 加载单独列字段
            separate_fields_path = os.path.join("config", "原始表单独列字段.json")
            if os.path.exists(separate_fields_path):
                with open(separate_fields_path, 'r', encoding='utf-8') as f:
                    separate_data = json.load(f)
                    if separate_data and isinstance(separate_data, list) and len(separate_data) > 0:
                        separate_fields = list(separate_data[0].keys())
                    else:
                        separate_fields = []
            else:
                separate_fields = ["id", "样车编号", "问题简述", "问题描述_QTM_DTS_DTM",
                                 "纬度", "经度", "测试软件版本", "问题日期_QTM_DTS_DTM",
                                 "具体时间", "问题提出人", "问题有效性"]
            
            # 加载固定字段
            fixed_fields_path = os.path.join("config", "原始表固定字段.json")
            if os.path.exists(fixed_fields_path):
                with open(fixed_fields_path, 'r', encoding='utf-8') as f:
                    fixed_data = json.load(f)
                    if fixed_data and isinstance(fixed_data, list) and len(fixed_data) > 0:
                        fixed_fields = list(fixed_data[0].keys())
                    else:
                        fixed_fields = []
            else:
                fixed_fields = ["道路类型", "功能模式", "位置", "行车问题分类", "泊车问题分类", 
                              "接管类型", "日志数据批次", "dat数据批次", "dat数据", 
                              "严重度_QTM_DTS_DTM", "场景类型", "车速"]
            
            # 加载可变字段
            variable_fields_path = os.path.join("config", "原始表可变字段.json")
            if os.path.exists(variable_fields_path):
                with open(variable_fields_path, 'r', encoding='utf-8') as f:
                    variable_data = json.load(f)
                    if variable_data and isinstance(variable_data, list) and len(variable_data) > 0:
                        variable_fields = list(variable_data[0].keys())
                    else:
                        variable_fields = []
            else:
                variable_fields = ["数据来源", "测试环境", "天气条件", "路面状况", "交通状况", 
                                 "测试人员", "复现次数", "问题等级", "影响范围", "紧急程度", 
                                 "处理状态", "分配给", "预计完成时间", "实际完成时间", "验证结果", "备注"]
            
            return {
                "单独列字段": separate_fields,
                "固定字段": fixed_fields,
                "可变字段": variable_fields
            }
            
        except Exception as e:
            self.logger.error(f"加载字段分组配置失败: {e}")
            return {
                "单独列字段": [],
                "固定字段": [],
                "可变字段": []
            }

    def _extract_field_groups_from_json_files(self):
        """从实际的JSON文件中提取字段分组"""
        field_groups = {"单独列字段": [], "固定字段": [], "可变字段": []}

        # 分别加载三个字段文件并提取字段名
        field_files = [
            ("单独列字段", "原始表单独列字段.json"),
            ("固定字段", "原始表固定字段.json"),
            ("可变字段", "原始表可变字段.json")
        ]

        for field_type, filename in field_files:
            file_path = os.path.join("config", filename)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if data and isinstance(data, list) and len(data) > 0:
                            # 从JSON数组的第一个对象提取字段名
                            field_groups[field_type] = list(data[0].keys())
                            self.logger.info(f"从 {filename} 提取到 {len(field_groups[field_type])} 个字段")
                except Exception as e:
                    self.logger.error(f"读取字段文件 {filename} 失败: {e}")

        return field_groups

    def _are_field_groups_consistent(self, groups1, groups2):
        """检查两个字段分组是否一致"""
        try:
            # 获取所有字段
            fields1 = []
            fields2 = []

            for group_name in ["单独列字段", "固定字段", "可变字段"]:
                fields1.extend(groups1.get(group_name, []))
                fields2.extend(groups2.get(group_name, []))

            # 比较字段集合
            return set(fields1) == set(fields2)
        except Exception as e:
            self.logger.error(f"检查字段分组一致性失败: {e}")
            return False

    def _load_display_settings(self):
        """加载显示设置"""
        settings_path = os.path.join("config", "原始表显示设置.json")
        if os.path.exists(settings_path):
            try:
                with open(settings_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载显示设置失败: {e}")
        
        # 返回默认设置
        all_fields = (self.field_groups["单独列字段"] + 
                     self.field_groups["固定字段"] + 
                     self.field_groups["可变字段"])
        
        return {
            "visible_fields": all_fields,
            "field_order": all_fields
        }
    
    def _save_display_settings(self):
        """保存显示设置"""
        try:
            settings_path = os.path.join("config", "原始表显示设置.json")
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(self.display_settings, f, ensure_ascii=False, indent=2)
            self.logger.info("显示设置保存成功")
        except Exception as e:
            self.logger.error(f"保存显示设置失败: {e}")
    
    def _on_record_selected(self, record_id):
        """处理记录选中事件"""
        self.logger.debug(f"记录被选中: {record_id}")
    
    def _on_record_added(self, record_id):
        """处理记录添加事件"""
        self.logger.debug(f"记录已添加: {record_id}")
        self.status_message.emit(f"成功添加记录，ID: {record_id}")
        self.data_changed.emit()

    def _on_record_updated(self, record_id):
        """处理记录更新事件"""
        self.logger.debug(f"记录已更新: {record_id}")
        self.status_message.emit(f"记录已更新，ID: {record_id}")
        self.data_changed.emit()

    def _on_record_deleted(self, record_id):
        """处理记录删除事件"""
        self.logger.debug(f"记录已删除: {record_id}")
        self.status_message.emit(f"记录已删除，ID: {record_id}")
        self.data_changed.emit()
    
    def _on_display_settings_changed(self, settings):
        """处理显示设置变更"""
        try:
            self.display_settings = settings
            self._save_display_settings()
            
            # 应用设置到UI组件
            if self.original_widget:
                self.original_widget.apply_display_settings(settings)
            
            self.status_message.emit("原始记录表显示设置已更新")
        except Exception as e:
            self.logger.error(f"更新原始记录表显示设置失败: {e}")
            self.status_message.emit("更新显示设置失败")
