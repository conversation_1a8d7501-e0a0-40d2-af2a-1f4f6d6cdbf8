#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
import datetime
from logging.handlers import RotatingFileHandler

def setup_logging(log_directory=None, log_level=logging.INFO, optimize_startup=True):
    """
    设置日志系统

    参数:
        log_directory: 日志目录，默认为当前根目录下的app文件夹
        log_level: 日志级别，默认为INFO（优化启动性能）
        optimize_startup: 是否优化启动性能，减少日志输出
    """
    # 设置默认日志目录
    if log_directory is None:
        # 获取当前脚本所在的根目录
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        log_directory = os.path.join(current_dir, "app")
    
    # 确保日志目录存在
    os.makedirs(log_directory, exist_ok=True)
    
    # 生成日志文件名，包含日期
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    log_file = os.path.join(log_directory, f"tms_{today}.log")
    
    # 创建根日志记录器
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # 清除已有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建文件处理器，最大5MB，保留5个备份
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=5*1024*1024,  # 5MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(log_level)  # 设置文件处理器的日志级别
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)  # 设置控制台处理器的日志级别
    
    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 优化启动性能：设置第三方库和应用程序模块的日志级别
    if optimize_startup:
        # 第三方库日志级别设为WARNING，减少启动时的日志噪音
        logging.getLogger("PyQt5").setLevel(logging.WARNING)
        logging.getLogger("matplotlib").setLevel(logging.WARNING)
        logging.getLogger("pandas").setLevel(logging.WARNING)
        logging.getLogger("numpy").setLevel(logging.WARNING)

        # 应用程序特定模块的日志级别优化
        logging.getLogger("src.utils.color_utils").setLevel(logging.WARNING)
        logging.getLogger("src.views.color_fill_manager").setLevel(logging.WARNING)
        logging.getLogger("src.utils.chart_interaction_handler").setLevel(logging.WARNING)
        logging.getLogger("src.utils.chart_navigation_manager").setLevel(logging.WARNING)
        logging.getLogger("src.utils.dashboard_refresh_manager").setLevel(logging.WARNING)
        logging.getLogger("src.managers.original_table_manager").setLevel(logging.WARNING)

        # 启动时只记录关键信息
        if log_level <= logging.INFO:
            logging.info("日志系统初始化完成（启动优化模式）")
    else:
        # 标准模式：保持原有的日志级别设置
        logging.getLogger("PyQt5").setLevel(logging.WARNING)
        logging.getLogger("matplotlib").setLevel(logging.WARNING)
        logging.getLogger("src.utils.color_utils").setLevel(logging.WARNING)
        logging.getLogger("src.views.color_fill_manager").setLevel(logging.WARNING)
        logging.info("日志系统初始化完成")
    
    return logger 