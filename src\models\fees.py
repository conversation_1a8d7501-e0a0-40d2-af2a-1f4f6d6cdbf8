#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
import os
import datetime
import logging
import json
from src.utils.field_utils import filter_valid_fields

class Fees:
    """费用记录表数据模型类"""
    
    def __init__(self, db_path=None):
        """
        初始化费用记录表模型
        
        参数:
            db_path: 数据库路径
        """
        # 如果未提供数据库路径，使用默认路径
        if db_path is None:
            # 使用当前目录下的数据库文件
            self.db_path = os.path.join(os.getcwd(), '智能驾驶试验管控数据库.db')
        else:
            self.db_path = db_path
            
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库派生的JSON键集合
        self.db_derived_fixed_keys = set()
        self.db_derived_variable_keys = set()
        
        # 延迟测试数据库连接并加载派生键，减少启动时间
        self._connection_tested = False
    
    def _load_derived_json_keys(self, cursor):
        """从数据库加载已存在的固定和可变JSON字段的键"""
        try:
            cursor.execute("SELECT 费用记录表固定字段, 费用记录表可变字段 FROM fees LIMIT 1")
            row = cursor.fetchone()
            if row:
                if row[0] and isinstance(row[0], str):
                    try:
                        fixed_fields = json.loads(row[0])
                        self.db_derived_fixed_keys.update(fixed_fields.keys())
                    except json.JSONDecodeError:
                        self.logger.warning("无法从数据库解析 '费用记录表固定字段' 以派生键。")
                if row[1] and isinstance(row[1], str):
                    try:
                        variable_fields = json.loads(row[1])
                        self.db_derived_variable_keys.update(variable_fields.keys())
                    except json.JSONDecodeError:
                        self.logger.warning("无法从数据库解析 '费用记录表可变字段' 以派生键。")
            self.logger.info(f"从数据库派生的固定JSON键: {self.db_derived_fixed_keys}")
            self.logger.info(f"从数据库派生的可变JSON键: {self.db_derived_variable_keys}")
        except Exception as e:
            self.logger.error(f"从数据库加载派生的JSON键失败: {e}")

    def _test_connection_and_load_derived_keys(self):
        """测试数据库连接，检查fees表是否存在，并加载派生JSON键"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查fees表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fees'")
            if not cursor.fetchone():
                self.logger.error("数据库中不存在fees表")
                raise Exception("数据库中不存在fees表")
                
            # 获取表结构
            cursor.execute("PRAGMA table_info(fees)")
            columns = [col[1] for col in cursor.fetchall()]
            self.logger.info(f"成功连接到数据库，fees表包含以下列: {columns}")

            # 加载派生JSON键
            self._load_derived_json_keys(cursor)
            
        except Exception as e:
            self.logger.error(f"数据库连接测试或派生键加载失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_fees_records(self):
        """
        获取费用记录列表

        返回:
            费用记录数据DataFrame
        """
        # 延迟连接测试，只在首次调用时执行
        if not self._connection_tested:
            self._test_connection_and_load_derived_keys()
            self._connection_tested = True
            
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询所有记录
            query = "SELECT * FROM fees ORDER BY id ASC"
            df = pd.read_sql_query(query, conn)
            
            self.logger.info(f"获取到 {len(df)} 条费用记录")
            return df
            
        except Exception as e:
            self.logger.error(f"获取费用记录失败: {e}")
            return pd.DataFrame()
        finally:
            if conn:
                conn.close()
    
    def add_fees_record(self, data, field_definitions=None, field_groups=None):
        """
        添加新的费用记录
        参数:
            data: 包含记录字段的字典
            field_definitions: 字段定义（用于类型判断）
            field_groups: 字段分组dict，如{"单独列字段": [...], "固定字段": [...], "可变字段": [...]}，用于分流
        返回:
            新记录的ID，如果失败则返回None
        """
        self.logger.debug(f"ENTER add_fees_record with data: {data}")
        if '全不选' in data:
            self.logger.debug("add_fees_record: Removing '全不选' key.")
            del data['全不选']
        if '全选' in data:
            self.logger.debug("add_fees_record: Removing '全选' key.")
            del data['全选']
        self.logger.debug(f"Data after initial cleaning in add_fees_record: {data}")
        
        # 确保 field_groups 存在
        if not field_groups:
            self.logger.error("field_groups is required for proper field classification")
            return None
        
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 分离字段
            single_column_fields = field_groups.get("单独列字段", [])
            fixed_fields = field_groups.get("固定字段", [])
            variable_fields = field_groups.get("可变字段", [])
            
            # 构建插入语句
            insert_columns = []
            insert_values_placeholders = []
            insert_params = []
            
            # 处理单独列字段
            single_column_data = {}
            for field in single_column_fields:
                if field in data:
                    single_column_data[field] = data[field]
            
            # 处理固定字段JSON
            fixed_json_data = {}
            for field in fixed_fields:
                if field in data:
                    fixed_json_data[field] = data[field]
            
            # 处理可变字段JSON
            variable_json_data = {}
            for field in variable_fields:
                if field in data:
                    variable_json_data[field] = data[field]
            
            # 添加单独列字段到SQL
            for field_name, field_value in single_column_data.items():
                if field_name != 'id':  # 跳过id字段
                    insert_columns.append(f'"{field_name}"')
                    insert_values_placeholders.append('?')
                    insert_params.append(field_value)
            
            # 添加固定字段JSON
            insert_columns.append('"费用记录表固定字段"')
            insert_values_placeholders.append('?')
            insert_params.append(json.dumps(fixed_json_data, ensure_ascii=False))
            
            # 添加可变字段JSON
            insert_columns.append('"费用记录表可变字段"')
            insert_values_placeholders.append('?')
            insert_params.append(json.dumps(variable_json_data, ensure_ascii=False))
            
            # 构建并执行SQL
            columns_sql_part = ", ".join(insert_columns)
            placeholders_sql_part = ", ".join(insert_values_placeholders)
            sql_query = f'INSERT INTO fees ({columns_sql_part}) VALUES ({placeholders_sql_part})'
            
            self.logger.debug(f"Executing SQL for insert: {sql_query} with params: {insert_params}")
            cursor.execute(sql_query, tuple(insert_params))
            new_record_id = cursor.lastrowid
            conn.commit()
            
            self.logger.info(f"费用记录添加成功，ID: {new_record_id}")
            return new_record_id
            
        except Exception as e:
            self.logger.error(f"添加费用记录失败: {e}")
            if conn:
                conn.rollback()
            return None
        finally:
            if conn:
                conn.close()
    
    def update_fees_record(self, record_id, data, field_definitions=None, field_groups=None):
        """
        更新费用记录
        参数:
            record_id: 记录ID
            data: 包含更新字段的字典
            field_definitions: 字段定义
            field_groups: 字段分组
        返回:
            是否更新成功
        """
        if not field_groups:
            self.logger.error("field_groups is required for proper field classification")
            return False
        
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 分离字段
            single_column_fields = field_groups.get("单独列字段", [])
            fixed_fields = field_groups.get("固定字段", [])
            variable_fields = field_groups.get("可变字段", [])
            
            # 构建更新语句
            update_columns = []
            update_params = []
            
            # 处理单独列字段
            for field in single_column_fields:
                if field in data and field != 'id':
                    update_columns.append(f'"{field}" = ?')
                    update_params.append(data[field])
            
            # 处理固定字段JSON - 先读取现有数据，然后合并
            fixed_json_data = {}
            variable_json_data = {}

            # 读取现有的JSON数据
            cursor.execute('SELECT "费用记录表固定字段", "费用记录表可变字段" FROM fees WHERE id = ?', (record_id,))
            existing_data = cursor.fetchone()

            if existing_data:
                # 解析现有的固定字段JSON
                if existing_data[0]:
                    try:
                        fixed_json_data = json.loads(existing_data[0])
                    except (json.JSONDecodeError, TypeError):
                        fixed_json_data = {}

                # 解析现有的可变字段JSON
                if existing_data[1]:
                    try:
                        variable_json_data = json.loads(existing_data[1])
                    except (json.JSONDecodeError, TypeError):
                        variable_json_data = {}

            # 合并新的固定字段数据
            fixed_fields_updated = False
            for field in fixed_fields:
                if field in data:
                    fixed_json_data[field] = data[field]
                    fixed_fields_updated = True

            if fixed_fields_updated:
                update_columns.append('"费用记录表固定字段" = ?')
                update_params.append(json.dumps(fixed_json_data, ensure_ascii=False))

            # 合并新的可变字段数据
            variable_fields_updated = False
            for field in variable_fields:
                if field in data:
                    variable_json_data[field] = data[field]
                    variable_fields_updated = True

            if variable_fields_updated:
                update_columns.append('"费用记录表可变字段" = ?')
                update_params.append(json.dumps(variable_json_data, ensure_ascii=False))
            
            # 添加修改时间
            update_columns.append('"修改时间" = ?')
            update_params.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # 添加WHERE条件的参数
            update_params.append(record_id)
            
            # 构建并执行SQL
            if update_columns:
                sql_query = f'UPDATE fees SET {", ".join(update_columns)} WHERE id = ?'
                cursor.execute(sql_query, tuple(update_params))
                conn.commit()
                
                self.logger.debug(f"费用记录更新成功，ID: {record_id}")
                return True
            else:
                self.logger.warning("没有需要更新的字段")
                return False
                
        except Exception as e:
            self.logger.error(f"更新费用记录失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def batch_update_fees_records(self, batch_updates, field_definitions=None, field_groups=None):
        """
        批量更新费用记录 - 性能优化版本

        参数:
            batch_updates: 批量更新数据列表，格式为 [{'id': record_id, 'data': update_data}, ...]
            field_definitions: 字段定义
            field_groups: 字段分组配置

        返回:
            int: 成功更新的记录数量
        """
        if not batch_updates:
            return 0

        if not field_groups:
            self.logger.error("field_groups is required for proper field classification")
            return 0

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            success_count = 0
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 🔧 性能优化：使用事务批量处理
            cursor.execute("BEGIN TRANSACTION")

            try:
                for update_item in batch_updates:
                    record_id = update_item['id']
                    data = update_item['data'].copy()

                    # 清理数据
                    if '全不选' in data:
                        del data['全不选']
                    if '全选' in data:
                        del data['全选']

                    # 分类字段
                    individual_fields = field_groups.get('单独列字段', field_groups.get('individual_fields', []))
                    fixed_fields = field_groups.get('固定字段', field_groups.get('fixed_fields', []))
                    variable_fields = field_groups.get('可变字段', field_groups.get('variable_fields', []))

                    update_columns = []
                    update_params = []

                    # 处理单独列字段
                    for field in individual_fields:
                        if field == 'id':
                            continue
                        if field in data:
                            update_columns.append(f'"{field}" = ?')
                            update_params.append(data[field])

                    # 处理固定字段JSON - 先读取现有数据，然后合并
                    fixed_json_data = {}
                    variable_json_data = {}

                    # 读取现有的JSON数据
                    cursor.execute('SELECT "费用记录表固定字段", "费用记录表可变字段" FROM fees WHERE id = ?', (record_id,))
                    existing_data = cursor.fetchone()

                    if existing_data:
                        # 解析现有的固定字段JSON
                        if existing_data[0]:
                            try:
                                fixed_json_data = json.loads(existing_data[0])
                            except (json.JSONDecodeError, TypeError):
                                fixed_json_data = {}

                        # 解析现有的可变字段JSON
                        if existing_data[1]:
                            try:
                                variable_json_data = json.loads(existing_data[1])
                            except (json.JSONDecodeError, TypeError):
                                variable_json_data = {}

                    # 合并新的固定字段数据
                    fixed_fields_updated = False
                    for field in fixed_fields:
                        if field in data:
                            fixed_json_data[field] = data[field]
                            fixed_fields_updated = True

                    if fixed_fields_updated:
                        update_columns.append('"费用记录表固定字段" = ?')
                        update_params.append(json.dumps(fixed_json_data, ensure_ascii=False))

                    # 合并新的可变字段数据
                    variable_fields_updated = False
                    for field in variable_fields:
                        if field in data:
                            variable_json_data[field] = data[field]
                            variable_fields_updated = True

                    if variable_fields_updated:
                        update_columns.append('"费用记录表可变字段" = ?')
                        update_params.append(json.dumps(variable_json_data, ensure_ascii=False))

                    # 添加修改时间
                    update_columns.append('"修改时间" = ?')
                    update_params.append(current_time)

                    # 添加WHERE条件的参数
                    update_params.append(record_id)

                    # 构建并执行SQL
                    if update_columns:
                        sql_query = f'UPDATE fees SET {", ".join(update_columns)} WHERE id = ?'
                        cursor.execute(sql_query, tuple(update_params))

                        if cursor.rowcount > 0:
                            success_count += 1

                # 🔧 提交事务
                cursor.execute("COMMIT")

                self.logger.info(f"批量更新费用记录完成，成功更新 {success_count}/{len(batch_updates)} 条记录")
                return success_count

            except Exception as e:
                # 🔧 回滚事务
                cursor.execute("ROLLBACK")
                self.logger.error(f"批量更新费用记录事务失败，已回滚: {e}")
                return 0

        except Exception as e:
            self.logger.error(f"批量更新费用记录失败: {e}")
            return 0
        finally:
            if conn:
                conn.close()
    
    def delete_fees_record(self, record_id):
        """
        删除费用记录
        参数:
            record_id: 记录ID
        返回:
            是否删除成功
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM fees WHERE id = ?", (record_id,))
            conn.commit()
            
            if cursor.rowcount > 0:
                self.logger.info(f"费用记录删除成功，ID: {record_id}")
                return True
            else:
                self.logger.warning(f"未找到要删除的费用记录，ID: {record_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除费用记录失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()
    
    def get_all_records(self):
        """
        获取所有费用记录 - 为了与其他表保持一致的接口

        返回:
            费用记录数据DataFrame
        """
        return self.get_fees_records()

    def get_all_columns(self):
        """
        获取fees表的所有列名

        返回:
            列名列表
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取表结构
            cursor.execute("PRAGMA table_info(fees)")
            columns = [col[1] for col in cursor.fetchall()]

            # 获取一行数据以解析JSON字段
            cursor.execute("SELECT 费用记录表固定字段, 费用记录表可变字段 FROM fees LIMIT 1")
            row = cursor.fetchone()

            # 如果有数据，解析JSON字段
            if row:
                if row[0]:
                    try:
                        fixed_fields = json.loads(row[0])
                        columns.extend(fixed_fields.keys())
                    except json.JSONDecodeError:
                        pass

                if row[1]:
                    try:
                        variable_fields = json.loads(row[1])
                        columns.extend(variable_fields.keys())
                    except json.JSONDecodeError:
                        pass

            return columns
        except Exception as e:
            self.logger.error(f"获取列名失败: {e}")
            return []
        finally:
            if conn:
                conn.close()
