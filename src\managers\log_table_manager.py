#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
试验日志表管理器
负责管理试验日志表的所有功能，包括数据操作、UI管理、配置管理等
"""

import os
import json
import logging
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

from src.models.log import Log

class LogTableManager(QObject):
    """试验日志表管理器"""
    
    # 信号定义
    status_message = pyqtSignal(str)  # 状态消息信号
    data_changed = pyqtSignal()       # 数据变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据模型
        self.log_model = Log()
        
        # 初始化UI组件
        self.log_widget = None
        
        # 初始化配置
        self.field_groups = self._load_field_groups()
        self.display_settings = self._load_display_settings()
    
    def create_widget(self):
        """创建试验日志表组件"""
        if self.log_widget is None:
            # 这里将在后续创建LogTableWidget时导入
            from src.views.log_table_widget import LogTableWidget
            self.log_widget = LogTableWidget()

            # 连接信号
            self.log_widget.record_selected.connect(self._on_record_selected)
            self.log_widget.record_added.connect(self._on_record_added)
            self.log_widget.record_updated.connect(self._on_record_updated)
            self.log_widget.record_deleted.connect(self._on_record_deleted)

            # 应用管理器中的显示设置到widget
            if self.display_settings:
                self.log_widget.apply_display_settings(self.display_settings)

            self.logger.info("试验日志表组件创建成功")

        return self.log_widget
    
    def get_widget(self):
        """获取试验日志表组件"""
        if self.log_widget is None:
            return self.create_widget()
        return self.log_widget
    
    def refresh_data(self):
        """刷新数据"""
        try:
            if self.log_widget:
                self.log_widget.load_data()
                self.status_message.emit("试验日志表数据已刷新")
                self.data_changed.emit()
        except Exception as e:
            self.logger.error(f"刷新试验日志表数据失败: {e}")
            self.status_message.emit("刷新数据失败")
    
    def add_record(self, data=None):
        """添加新记录"""
        try:
            if self.log_widget:
                if data is None:
                    # 使用默认数据
                    self.log_widget.add_record()
                else:
                    # 使用提供的数据
                    record_id = self.log_model.add_log_record(data, field_groups=self.field_groups)
                    if record_id:
                        self.refresh_data()
                        self.status_message.emit(f"成功添加记录，ID: {record_id}")
                    else:
                        self.status_message.emit("添加记录失败")
        except Exception as e:
            self.logger.error(f"添加试验日志记录失败: {e}")
            self.status_message.emit("添加记录失败")

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量
        """
        try:
            if self.log_widget and hasattr(self.log_widget, 'add_multiple_records'):
                added_record_ids = self.log_widget.add_multiple_records(count)
                if added_record_ids:
                    self.status_message.emit(f"成功批量添加 {len(added_record_ids)} 条试验日志记录")
                    self.data_changed.emit()
                else:
                    self.status_message.emit("批量添加试验日志记录失败")
                return added_record_ids
            else:
                self.status_message.emit("不支持批量添加功能")
                return []
        except Exception as e:
            self.logger.error(f"批量添加试验日志记录失败: {e}")
            self.status_message.emit("批量添加记录失败")
            return []

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            if self.log_widget:
                selected_ids = self.log_widget.get_selected_records()
                if not selected_ids:
                    QMessageBox.information(self.parent, "提示", "请先选择要删除的记录")
                    return
                
                reply = QMessageBox.question(
                    self.parent, 
                    "确认删除", 
                    f"确定要删除选中的 {len(selected_ids)} 条记录吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    success_count = 0
                    for record_id in selected_ids:
                        if self.log_model.delete_log_record(record_id):
                            success_count += 1
                    
                    self.refresh_data()
                    self.status_message.emit(f"成功删除 {success_count} 条记录")
        except Exception as e:
            self.logger.error(f"删除试验日志记录失败: {e}")
            self.status_message.emit("删除记录失败")
    
    def show_display_settings(self):
        """显示显示设置对话框"""
        try:
            # 这里将在后续创建LogDisplaySettingsDialog时导入
            from src.dialogs.log_display_settings_dialog import LogDisplaySettingsDialog
            dialog = LogDisplaySettingsDialog(self.parent)
            dialog.settings_changed.connect(self._on_display_settings_changed)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
            self.status_message.emit("显示设置失败")
    
    def import_field_json(self, json_data):
        """导入字段JSON配置"""
        try:
            if self.log_widget:
                self.log_widget.import_field_json(json_data)
                self.status_message.emit("字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入字段JSON配置失败: {e}")
            self.status_message.emit("导入字段JSON配置失败")

    def update_field_definitions(self, field_definitions):
        """更新字段定义"""
        try:
            if self.log_widget:
                # 重新加载字段分组配置
                self.field_groups = self._load_field_groups()
                # 更新widget的字段分组
                self.log_widget.field_groups = self.field_groups

                # 重新加载字段定义配置 - 修复：添加此行以确保字段定义配置文件被重新加载
                self.log_widget.reload_field_definitions()

                # 更新显示设置文件以包含新的字段
                self._update_display_settings_for_new_fields()

                # 重新加载数据以应用新的字段配置
                self.log_widget.load_data()
                self.status_message.emit("试验日志表字段定义已更新")
                self.logger.info("试验日志表字段定义更新成功")
        except Exception as e:
            self.logger.error(f"更新试验日志表字段定义失败: {e}")
            self.status_message.emit("更新字段定义失败")
    
    def export_data(self, file_path):
        """导出数据"""
        try:
            data = self.log_model.get_log_records()
            if not data.empty:
                data.to_excel(file_path, index=False)
                self.status_message.emit(f"数据已导出到: {file_path}")
            else:
                self.status_message.emit("没有数据可导出")
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            self.status_message.emit("导出数据失败")
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        try:
            # 定义默认字段分组
            default_groups = {
                "单独列字段": ["id", "日期", "样车编号", "工作内容", "修改时间"],
                "固定字段": ["记录人员", "工作时长", "工作地点", "天气条件", "温度", "工作状态"],
                "可变字段": ["备注", "问题记录", "解决方案", "下一步计划", "风险提示", "资源需求"]
            }

            # 尝试从新的配置文件路径加载（字段导入功能生成的文件）- 修复：使用config目录
            config_path = os.path.join("config", "log_field_groups.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 尝试从旧的字段文件构建字段分组（兼容旧系统）
            try:
                field_groups = {"单独列字段": [], "固定字段": [], "可变字段": []}

                # 加载单独列字段
                separate_path = os.path.join("config", "试验日志表单独列字段.json")
                if os.path.exists(separate_path):
                    with open(separate_path, 'r', encoding='utf-8') as f:
                        separate_data = json.load(f)
                        if separate_data and isinstance(separate_data, list) and len(separate_data) > 0:
                            field_groups["单独列字段"] = list(separate_data[0].keys())

                # 加载固定字段
                fixed_path = os.path.join("config", "试验日志表固定字段.json")
                if os.path.exists(fixed_path):
                    with open(fixed_path, 'r', encoding='utf-8') as f:
                        fixed_data = json.load(f)
                        if fixed_data and isinstance(fixed_data, list) and len(fixed_data) > 0:
                            field_groups["固定字段"] = list(fixed_data[0].keys())

                # 加载可变字段
                variable_path = os.path.join("config", "试验日志表可变字段.json")
                if os.path.exists(variable_path):
                    with open(variable_path, 'r', encoding='utf-8') as f:
                        variable_data = json.load(f)
                        if variable_data and isinstance(variable_data, list) and len(variable_data) > 0:
                            field_groups["可变字段"] = list(variable_data[0].keys())

                # 如果成功加载了任何字段，返回构建的字段分组
                if any(field_groups.values()):
                    self.logger.info("从旧的字段文件构建字段分组成功")
                    return field_groups

            except Exception as e:
                self.logger.warning(f"从旧字段文件构建字段分组失败: {e}")

            # 兼容旧的配置文件路径
            old_config_path = os.path.join("config", "试验日志表字段分组.json")
            if os.path.exists(old_config_path):
                with open(old_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            self.logger.info("使用默认试验日志表字段分组")
            return default_groups
        except Exception as e:
            self.logger.error(f"加载字段分组配置失败: {e}")
            return {
                "单独列字段": ["id", "日期", "样车编号", "工作内容", "修改时间"],
                "固定字段": ["记录人员"],
                "可变字段": ["备注"]
            }
    
    def _load_display_settings(self):
        """加载显示设置"""
        try:
            config_path = os.path.join("config", "试验日志表显示设置.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.logger.error(f"加载显示设置失败: {e}")
            return {}
    
    def _on_record_selected(self, record_id):
        """记录选中事件处理"""
        self.logger.debug(f"选中记录: {record_id}")
    
    def _on_record_added(self, record_id):
        """记录添加事件处理"""
        self.logger.debug(f"添加记录: {record_id}")
        self.data_changed.emit()

    def _on_record_updated(self, record_id):
        """记录更新事件处理"""
        self.logger.debug(f"更新记录: {record_id}")
        self.data_changed.emit()

    def _on_record_deleted(self, record_id):
        """记录删除事件处理"""
        self.logger.debug(f"删除记录: {record_id}")
        self.data_changed.emit()

    def _on_display_settings_changed(self, settings):
        """处理显示设置变更"""
        try:
            self.display_settings = settings
            self._save_display_settings()

            # 应用设置到UI组件
            if self.log_widget:
                self.log_widget.apply_display_settings(settings)

            self.status_message.emit("试验日志表显示设置已更新")
            self.refresh_data()
        except Exception as e:
            self.logger.error(f"更新试验日志表显示设置失败: {e}")
            self.status_message.emit("更新显示设置失败")

    def _update_display_settings_for_new_fields(self):
        """根据新的字段分组更新显示设置文件"""
        try:
            # 获取所有字段
            all_fields = []
            all_fields.extend(self.field_groups.get("单独列字段", []))
            all_fields.extend(self.field_groups.get("固定字段", []))
            all_fields.extend(self.field_groups.get("可变字段", []))

            # 加载现有的显示设置
            config_path = os.path.join("config", "试验日志表显示设置.json")
            current_settings = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    current_settings = json.load(f)

            # 获取现有的可见字段和字段顺序
            current_visible = set(current_settings.get("visible_fields", []))
            current_order = current_settings.get("field_order", [])
            current_widths = current_settings.get("column_widths", {})

            # 构建新的字段顺序，保持现有字段的顺序，新字段添加到末尾
            new_field_order = []
            new_visible_fields = []

            # 先添加现有顺序中仍然存在的字段
            for field in current_order:
                if field in all_fields:
                    new_field_order.append(field)
                    if field in current_visible:
                        new_visible_fields.append(field)

            # 添加新字段到末尾（默认可见）
            for field in all_fields:
                if field not in new_field_order:
                    new_field_order.append(field)
                    new_visible_fields.append(field)

            # 清理列宽设置，移除不存在的字段
            new_widths = {k: v for k, v in current_widths.items() if k in all_fields}

            # 构建新的显示设置
            new_settings = {
                "visible_fields": new_visible_fields,
                "field_order": new_field_order,
                "column_widths": new_widths
            }

            # 保存更新后的显示设置
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=4)

            # 更新内存中的显示设置
            self.display_settings = new_settings

            self.logger.info("试验日志表显示设置已根据新字段分组更新")

        except Exception as e:
            self.logger.error(f"更新试验日志表显示设置失败: {e}")
    
    def get_data_model(self):
        """获取数据模型"""
        return self.log_model
    
    def get_field_groups(self):
        """获取字段分组"""
        return self.field_groups
    
    def set_field_groups(self, field_groups):
        """设置字段分组"""
        self.field_groups = field_groups
        # 保存到配置文件
        try:
            config_path = os.path.join("config", "试验日志表字段分组.json")
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(field_groups, f, ensure_ascii=False, indent=4)
        except Exception as e:
            self.logger.error(f"保存字段分组配置失败: {e}")
    
    def get_display_settings(self):
        """获取显示设置"""
        return self.display_settings
    
    def set_display_settings(self, settings):
        """设置显示设置"""
        self.display_settings = settings
        # 保存到配置文件
        self._save_display_settings()

    def _save_display_settings(self):
        """保存显示设置"""
        try:
            config_path = os.path.join("config", "试验日志表显示设置.json")
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.display_settings, f, ensure_ascii=False, indent=2)
            self.logger.info("试验日志表显示设置保存成功")
        except Exception as e:
            self.logger.error(f"保存试验日志表显示设置失败: {e}")
