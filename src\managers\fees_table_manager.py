#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
费用记录表管理器
负责管理费用记录表的所有功能，包括数据操作、UI管理、配置管理等
"""

import os
import json
import logging
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

from src.models.fees import Fees

class FeesTableManager(QObject):
    """费用记录表管理器"""
    
    # 信号定义
    status_message = pyqtSignal(str)  # 状态消息信号
    data_changed = pyqtSignal()       # 数据变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据模型
        self.fees_model = Fees()
        
        # 初始化UI组件
        self.fees_widget = None
        
        # 初始化配置
        self.field_groups = self._load_field_groups()
        self.display_settings = self._load_display_settings()
    
    def create_widget(self):
        """创建费用记录表组件"""
        if self.fees_widget is None:
            # 这里将在后续创建FeesTableWidget时导入
            from src.views.fees_table_widget import FeesTableWidget
            self.fees_widget = FeesTableWidget()

            # 连接信号
            self.fees_widget.record_selected.connect(self._on_record_selected)
            self.fees_widget.record_added.connect(self._on_record_added)
            self.fees_widget.record_updated.connect(self._on_record_updated)
            self.fees_widget.record_deleted.connect(self._on_record_deleted)

            # 应用管理器中的显示设置到widget
            if self.display_settings:
                self.fees_widget.apply_display_settings(self.display_settings)

            self.logger.info("费用记录表组件创建成功")

        return self.fees_widget
    
    def get_widget(self):
        """获取费用记录表组件"""
        if self.fees_widget is None:
            return self.create_widget()
        return self.fees_widget
    
    def refresh_data(self):
        """刷新数据"""
        try:
            if self.fees_widget:
                self.fees_widget.load_data()
                self.status_message.emit("费用记录表数据已刷新")
                self.data_changed.emit()
        except Exception as e:
            self.logger.error(f"刷新费用记录表数据失败: {e}")
            self.status_message.emit("刷新数据失败")
    
    def add_record(self, data=None):
        """添加新记录"""
        try:
            if self.fees_widget:
                if data is None:
                    # 使用默认数据
                    self.fees_widget.add_record()
                else:
                    # 使用提供的数据
                    record_id = self.fees_model.add_fees_record(data, field_groups=self.field_groups)
                    if record_id:
                        self.refresh_data()
                        self.status_message.emit(f"成功添加记录，ID: {record_id}")
                    else:
                        self.status_message.emit("添加记录失败")
        except Exception as e:
            self.logger.error(f"添加费用记录失败: {e}")
            self.status_message.emit("添加记录失败")

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量
        """
        try:
            if self.fees_widget and hasattr(self.fees_widget, 'add_multiple_records'):
                added_record_ids = self.fees_widget.add_multiple_records(count)
                if added_record_ids:
                    self.status_message.emit(f"成功批量添加 {len(added_record_ids)} 条记录")
                    self.data_changed.emit()
                else:
                    self.status_message.emit("批量添加记录失败")
                return added_record_ids
            else:
                self.status_message.emit("不支持批量添加功能")
                return []
        except Exception as e:
            self.logger.error(f"批量添加费用记录失败: {e}")
            self.status_message.emit("批量添加记录失败")
            return []
    
    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            if self.fees_widget:
                selected_ids = self.fees_widget.get_selected_records()
                if not selected_ids:
                    QMessageBox.information(self.parent, "提示", "请先选择要删除的记录")
                    return
                
                reply = QMessageBox.question(
                    self.parent, 
                    "确认删除", 
                    f"确定要删除选中的 {len(selected_ids)} 条记录吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    success_count = 0
                    for record_id in selected_ids:
                        if self.fees_model.delete_fees_record(record_id):
                            success_count += 1
                    
                    self.refresh_data()
                    self.status_message.emit(f"成功删除 {success_count} 条记录")
        except Exception as e:
            self.logger.error(f"删除费用记录失败: {e}")
            self.status_message.emit("删除记录失败")
    
    def show_display_settings(self):
        """显示显示设置对话框"""
        try:
            # 这里将在后续创建FeesDisplaySettingsDialog时导入
            from src.dialogs.fees_display_settings_dialog import FeesDisplaySettingsDialog
            dialog = FeesDisplaySettingsDialog(self.parent)
            dialog.settings_changed.connect(self._on_display_settings_changed)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
            self.status_message.emit("显示设置失败")
    
    def import_field_json(self, json_data):
        """导入字段JSON配置"""
        try:
            if self.fees_widget:
                self.fees_widget.import_field_json(json_data)
                self.status_message.emit("字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入字段JSON配置失败: {e}")
            self.status_message.emit("导入字段JSON配置失败")

    def update_field_definitions(self, field_definitions):
        """更新字段定义"""
        try:
            if self.fees_widget:
                # 重新加载字段分组配置
                self.field_groups = self._load_field_groups()
                # 更新widget的字段分组
                self.fees_widget.field_groups = self.field_groups

                # 重新加载字段定义配置 - 修复：添加此行以确保字段定义配置文件被重新加载
                self.fees_widget.reload_field_definitions()

                # 更新显示设置文件以包含新的字段
                self._update_display_settings_for_new_fields()

                # 重新加载数据以应用新的字段配置
                self.fees_widget.load_data()
                self.status_message.emit("费用记录表字段定义已更新")
                self.logger.info("费用记录表字段定义更新成功")
        except Exception as e:
            self.logger.error(f"更新费用记录表字段定义失败: {e}")
            self.status_message.emit("更新字段定义失败")
    
    def export_data(self, file_path):
        """导出数据"""
        try:
            data = self.fees_model.get_fees_records()
            if not data.empty:
                data.to_excel(file_path, index=False)
                self.status_message.emit(f"数据已导出到: {file_path}")
            else:
                self.status_message.emit("没有数据可导出")
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            self.status_message.emit("导出数据失败")
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        try:
            # 1. 优先尝试从新系统文件加载（字段导入功能生成的文件）
            config_path = os.path.join("config", "fees_field_groups.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    new_groups = json.load(f)

                # 验证新系统文件是否与实际JSON文件同步
                actual_groups = self._extract_field_groups_from_json_files()
                if self._are_field_groups_consistent(new_groups, actual_groups):
                    self.logger.info("使用新系统字段分组文件（已验证与实际文件一致）")
                    return new_groups
                else:
                    self.logger.warning("新系统字段分组文件与实际JSON文件不一致，使用实际文件重建")
                    # 更新新系统文件以保持同步
                    try:
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(actual_groups, f, ensure_ascii=False, indent=4)
                        self.logger.info("已更新新系统字段分组文件以保持同步")
                    except Exception as e:
                        self.logger.error(f"更新新系统字段分组文件失败: {e}")
                    return actual_groups

            # 2. 兼容旧的配置文件路径
            old_config_path = os.path.join("config", "费用记录表字段分组.json")
            if os.path.exists(old_config_path):
                with open(old_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 3. 从旧系统文件构建字段分组（兼容性处理）
            actual_groups = self._extract_field_groups_from_json_files()
            if any(actual_groups.values()):
                # 保存为新系统文件以便下次使用
                try:
                    os.makedirs(os.path.dirname(config_path), exist_ok=True)
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(actual_groups, f, ensure_ascii=False, indent=4)
                    self.logger.info(f"从实际JSON文件构建字段分组并保存到 {config_path}")
                except Exception as e:
                    self.logger.error(f"保存字段分组文件失败: {e}")

                return actual_groups

            # 4. 如果都失败了，返回最小默认配置
            return {
                "单独列字段": ["id", "日期", "费用类型", "金额", "样车编号", "记录人员", "发票金额", "发票数量", "修改时间"],
                "固定字段": ["支出品类", "支出方式", "支出对象"],
                "可变字段": ["备注"]
            }

        except Exception as e:
            self.logger.error(f"加载字段分组配置失败: {e}")
            return {
                "单独列字段": ["id", "日期", "费用类型", "金额", "样车编号", "记录人员", "发票金额", "发票数量", "修改时间"],
                "固定字段": ["支出品类", "支出方式", "支出对象"],
                "可变字段": ["备注"]
            }

    def _extract_field_groups_from_json_files(self):
        """从实际的JSON文件中提取字段分组"""
        field_groups = {"单独列字段": [], "固定字段": [], "可变字段": []}

        # 分别加载三个字段文件并提取字段名
        field_files = [
            ("单独列字段", "费用记录表单独列字段.json"),
            ("固定字段", "费用记录表固定字段.json"),
            ("可变字段", "费用记录表可变字段.json")
        ]

        for field_type, filename in field_files:
            file_path = os.path.join("config", filename)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if data and isinstance(data, list) and len(data) > 0:
                            # 从JSON数组的第一个对象提取字段名
                            field_groups[field_type] = list(data[0].keys())
                            self.logger.info(f"从 {filename} 提取到 {len(field_groups[field_type])} 个字段")
                except Exception as e:
                    self.logger.error(f"读取字段文件 {filename} 失败: {e}")

        return field_groups

    def _are_field_groups_consistent(self, groups1, groups2):
        """检查两个字段分组是否一致"""
        try:
            # 获取所有字段
            fields1 = []
            fields2 = []

            for group_name in ["单独列字段", "固定字段", "可变字段"]:
                fields1.extend(groups1.get(group_name, []))
                fields2.extend(groups2.get(group_name, []))

            # 比较字段集合
            return set(fields1) == set(fields2)
        except Exception as e:
            self.logger.error(f"检查字段分组一致性失败: {e}")
            return False

    def _load_display_settings(self):
        """加载显示设置"""
        try:
            config_path = os.path.join("config", "费用记录表显示设置.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.logger.error(f"加载显示设置失败: {e}")
            return {}
    
    def _on_record_selected(self, record_id):
        """记录选中事件处理"""
        self.logger.debug(f"选中记录: {record_id}")
    
    def _on_record_added(self, record_id):
        """记录添加事件处理"""
        self.logger.debug(f"添加记录: {record_id}")
        self.data_changed.emit()

    def _on_record_updated(self, record_id):
        """记录更新事件处理"""
        self.logger.debug(f"更新记录: {record_id}")
        self.data_changed.emit()

    def _on_record_deleted(self, record_id):
        """记录删除事件处理"""
        self.logger.debug(f"删除记录: {record_id}")
        self.data_changed.emit()

    def _on_display_settings_changed(self, settings):
        """处理显示设置变更"""
        try:
            self.display_settings = settings
            self._save_display_settings()

            # 应用设置到UI组件
            if self.fees_widget:
                self.fees_widget.apply_display_settings(settings)

            self.status_message.emit("费用记录表显示设置已更新")
            self.refresh_data()
        except Exception as e:
            self.logger.error(f"更新费用记录表显示设置失败: {e}")
            self.status_message.emit("更新显示设置失败")

    def _update_display_settings_for_new_fields(self):
        """根据新的字段分组更新显示设置文件"""
        try:
            # 获取所有字段
            all_fields = []
            all_fields.extend(self.field_groups.get("单独列字段", []))
            all_fields.extend(self.field_groups.get("固定字段", []))
            all_fields.extend(self.field_groups.get("可变字段", []))

            # 加载现有的显示设置
            config_path = os.path.join("config", "费用记录表显示设置.json")
            current_settings = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    current_settings = json.load(f)

            # 获取现有的可见字段和字段顺序
            current_visible = set(current_settings.get("visible_fields", []))
            current_order = current_settings.get("field_order", [])
            current_widths = current_settings.get("column_widths", {})

            # 构建新的字段顺序，保持现有字段的顺序，新字段添加到末尾
            new_field_order = []
            new_visible_fields = []

            # 先添加现有顺序中仍然存在的字段
            for field in current_order:
                if field in all_fields:
                    new_field_order.append(field)
                    if field in current_visible:
                        new_visible_fields.append(field)

            # 添加新字段到末尾（默认可见）
            for field in all_fields:
                if field not in new_field_order:
                    new_field_order.append(field)
                    new_visible_fields.append(field)

            # 清理列宽设置，移除不存在的字段
            new_widths = {k: v for k, v in current_widths.items() if k in all_fields}

            # 构建新的显示设置
            new_settings = {
                "visible_fields": new_visible_fields,
                "field_order": new_field_order,
                "column_widths": new_widths
            }

            # 保存更新后的显示设置
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=4)

            # 更新内存中的显示设置
            self.display_settings = new_settings

            self.logger.info("费用记录表显示设置已根据新字段分组更新")

        except Exception as e:
            self.logger.error(f"更新费用记录表显示设置失败: {e}")

    def get_data_model(self):
        """获取数据模型"""
        return self.fees_model

    def get_field_groups(self):
        """获取字段分组"""
        return self.field_groups
    
    def set_field_groups(self, field_groups):
        """设置字段分组"""
        self.field_groups = field_groups
        # 保存到配置文件
        try:
            config_path = os.path.join("config", "费用记录表字段分组.json")
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(field_groups, f, ensure_ascii=False, indent=4)
        except Exception as e:
            self.logger.error(f"保存字段分组配置失败: {e}")
    
    def get_display_settings(self):
        """获取显示设置"""
        return self.display_settings
    
    def set_display_settings(self, settings):
        """设置显示设置"""
        self.display_settings = settings
        # 保存到配置文件
        self._save_display_settings()

    def _save_display_settings(self):
        """保存显示设置"""
        try:
            config_path = os.path.join("config", "费用记录表显示设置.json")
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.display_settings, f, ensure_ascii=False, indent=2)
            self.logger.info("费用记录表显示设置保存成功")
        except Exception as e:
            self.logger.error(f"保存费用记录表显示设置失败: {e}")
