#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd # type: ignore
import os
import datetime
import logging
import json
from src.utils.field_utils import filter_valid_fields

class Problem:
    """问题数据模型类"""
    
    def __init__(self, db_path=None):
        """
        初始化问题模型
        
        参数:
            db_path: 数据库路径
        """
        # 如果未提供数据库路径，使用默认路径
        if db_path is None:
            # 使用当前目录下的数据库文件
            self.db_path = os.path.join(os.getcwd(), '智能驾驶试验管控数据库.db')
        else:
            self.db_path = db_path
            
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库派生的JSON键集合
        self.db_derived_fixed_keys = set()
        self.db_derived_variable_keys = set()
        
        # 延迟测试数据库连接并加载派生键，减少启动时间
        self._connection_tested = False
    
    def _load_derived_json_keys(self, cursor):
        """从数据库加载已存在的固定和可变JSON字段的键 (由_test_connection_and_load_derived_keys调用)"""
        try:
            cursor.execute("SELECT 问题表固定字段, 问题表可变字段 FROM Problem LIMIT 1")
            row = cursor.fetchone()
            if row:
                if row[0] and isinstance(row[0], str):
                    try:
                        fixed_fields = json.loads(row[0])
                        self.db_derived_fixed_keys.update(fixed_fields.keys())
                    except json.JSONDecodeError:
                        self.logger.warning("无法从数据库解析 '问题表固定字段' 以派生键。")
                if row[1] and isinstance(row[1], str):
                    try:
                        variable_fields = json.loads(row[1])
                        self.db_derived_variable_keys.update(variable_fields.keys())
                    except json.JSONDecodeError:
                        self.logger.warning("无法从数据库解析 '问题表可变字段' 以派生键。")
            self.logger.info(f"从数据库派生的固定JSON键: {self.db_derived_fixed_keys}")
            self.logger.info(f"从数据库派生的可变JSON键: {self.db_derived_variable_keys}")
        except Exception as e:
            self.logger.error(f"从数据库加载派生的JSON键失败: {e}")

    def _test_connection_and_load_derived_keys(self):
        """测试数据库连接，检查Problem表是否存在，并加载派生JSON键"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查Problem表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='Problem'")
            if not cursor.fetchone():
                self.logger.error("数据库中不存在Problem表")
                raise Exception("数据库中不存在Problem表")
                
            # 获取表结构
            cursor.execute("PRAGMA table_info(Problem)")
            columns = [col[1] for col in cursor.fetchall()]
            self.logger.info(f"成功连接到数据库，Problem表包含以下列: {columns}")

            # 加载派生JSON键
            self._load_derived_json_keys(cursor)
            
        except Exception as e:
            self.logger.error(f"数据库连接测试或派生键加载失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_problems(self, project_id=None, file_id=None):
        """
        获取问题列表

        参数:
            project_id: 项目ID（可选）
            file_id: 文件ID（可选）

        返回:
            问题数据DataFrame
        """
        # 延迟连接测试，只在首次调用时执行
        if not self._connection_tested:
            self._test_connection_and_load_derived_keys()
            self._connection_tested = True

        conn = None
        try:
            logging.info(f"🔍 get_problems: 连接数据库 {self.db_path}")
            conn = sqlite3.connect(self.db_path)

            # 直接从Problem表查询数据
            query = "SELECT * FROM Problem"

            # 暂时不使用项目和文件过滤，因为新的Problem表可能没有这些字段
            # 如果将来需要，可以添加相应的过滤条件

            # 按id递增排序
            query += " ORDER BY id ASC"
            logging.info(f"🔍 get_problems: 执行查询: {query}")

            # 读取数据
            df = pd.read_sql_query(query, conn)
            logging.info(f"🔍 get_problems: 查询结果行数: {len(df)}")
            logging.info(f"🔍 get_problems: 原始列: {list(df.columns) if not df.empty else '无数据'}")
            
            # 处理JSON字段
            if not df.empty and '问题表固定字段' in df.columns and '问题表可变字段' in df.columns:
                logging.info(f"🔍 get_problems: 开始处理JSON字段，共 {len(df)} 行")
                # 解析JSON字段
                for index, row in df.iterrows():
                    if index < 3:  # 只记录前3行的详细信息
                        logging.info(f"🔍 get_problems: 处理第 {index} 行JSON字段")

                    # 🔧 修复：定义不应被覆盖的重要数据库列
                    protected_columns = {'id', '修改时间', '试验类型', '样车编号', '问题编号_QTM', '问题编号_DTX'}

                    # 解析问题表固定字段
                    if row['问题表固定字段'] and isinstance(row['问题表固定字段'], str):
                        try:
                            fixed_fields = json.loads(row['问题表固定字段'])
                            for key, value in fixed_fields.items():
                                # 🔧 修复：不覆盖重要的数据库列
                                if key not in protected_columns:
                                    df.at[index, key] = value
                                else:
                                    if index < 3:  # 只记录前3行的详细信息
                                        logging.debug(f"🔧 跳过覆盖受保护列 '{key}': {value}")
                        except json.JSONDecodeError:
                            self.logger.warning(f"行 {index} 的问题表固定字段不是有效的JSON: {row['问题表固定字段']}")

                    # 解析问题表可变字段
                    if row['问题表可变字段'] and isinstance(row['问题表可变字段'], str):
                        try:
                            variable_fields = json.loads(row['问题表可变字段'])
                            for key, value in variable_fields.items():
                                # 🔧 修复：不覆盖重要的数据库列
                                if key not in protected_columns:
                                    df.at[index, key] = value
                                else:
                                    if index < 3:  # 只记录前3行的详细信息
                                        logging.debug(f"🔧 跳过覆盖受保护列 '{key}': {value}")
                        except json.JSONDecodeError:
                            self.logger.warning(f"行 {index} 的问题表可变字段不是有效的JSON: {row['问题表可变字段']}")

                logging.info(f"🔍 get_problems: JSON字段处理完成，最终列数: {len(df.columns)}")
                logging.info(f"🔍 get_problems: 最终列: {list(df.columns)}")

            logging.info(f"🔍 get_problems: 返回数据形状: {df.shape}")
            return df
        except Exception as e:
            self.logger.error(f"获取问题列表失败: {e}")
            return pd.DataFrame()
        finally:
            if conn:
                conn.close()

    def find_problem_by_conditions(self, conditions):
        """
        根据条件查找问题

        参数:
            conditions: 查询条件字典，格式为 {字段名: 值}

        返回:
            匹配的问题ID，如果未找到则返回None
        """
        if not conditions:
            return None

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建WHERE子句
            where_clauses = []
            params = []

            for field_name, value in conditions.items():
                # 对字段名进行转义
                escaped_field = f'"{field_name}"'
                where_clauses.append(f"{escaped_field} = ?")
                params.append(value)

            if not where_clauses:
                return None

            where_sql = " AND ".join(where_clauses)
            sql = f"SELECT id FROM Problem WHERE {where_sql} LIMIT 1"

            self.logger.debug(f"查找问题SQL: {sql}, 参数: {params}")
            cursor.execute(sql, params)
            result = cursor.fetchone()

            return result[0] if result else None

        except Exception as e:
            self.logger.error(f"根据条件查找问题失败: {e}")
            return None
        finally:
            if conn:
                conn.close()
    
    def add_problem(self, data, field_definitions=None, field_groups=None):
        """
        添加新问题
        参数:
            data: 包含问题字段的字典
            field_definitions: 字段定义（用于类型判断）
            field_groups: 字段分组dict，如{"单独列字段": [...], "固定字段": [...], "可变字段": [...]}，用于分流
        返回:
            新问题的ID，如果失败则返回None
        """
        self.logger.debug(f"ENTER add_problem with data: {data}")
        if '全不选' in data:
            self.logger.debug("add_problem: Removing '全不选' key.")
            del data['全不选']
        if '全选' in data:
            self.logger.debug("add_problem: Removing '全选' key.")
            del data['全选']
        self.logger.debug(f"Data after initial cleaning in add_problem: {data}")
        
        # 确保 field_groups 存在
        if not field_groups:
            self.logger.error("field_groups is required for proper field classification")
            return None
            
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(Problem)")
            actual_table_columns = [info[1] for info in cursor.fetchall()]
            insertable_physical_cols = [col for col in actual_table_columns if col not in ('id', '问题表固定字段', '问题表可变字段')]
            
            # 字段分流
            fixed_json_data = {}
            variable_json_data = {}
            physical_col_values = {}
            
            # 根据 field_groups 进行字段分类
            for key, value in data.items():
                if key in field_groups.get("单独列字段", []):
                    if key in insertable_physical_cols:
                        physical_col_values[key] = value
                elif key in field_groups.get("固定字段", []):
                    fixed_json_data[key] = value
                elif key in field_groups.get("可变字段", []):
                    variable_json_data[key] = value
                else:
                    # 未在 field_groups 中定义的字段，默认放入可变字段
                    variable_json_data[key] = value
            
            # 确保所有固定字段和可变字段都有值（即使是空值）
            for field in field_groups.get("固定字段", []):
                if field not in fixed_json_data:
                    fixed_json_data[field] = None
            
            for field in field_groups.get("可变字段", []):
                if field not in variable_json_data:
                    variable_json_data[field] = None
            
            # 填充物理列默认值
            for col_name in insertable_physical_cols:
                if col_name == "修改时间":
                    physical_col_values[col_name] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                elif col_name not in physical_col_values:
                    physical_col_values[col_name] = None
            
            # 构建SQL
            insert_columns = []
            insert_values_placeholders = []
            insert_params = []
            
            # 添加物理列
            for col_name in insertable_physical_cols:
                insert_columns.append(f'"{col_name}"')
                insert_values_placeholders.append('?')
                insert_params.append(physical_col_values.get(col_name, None))
            
            # 添加固定字段JSON
            insert_columns.append('"问题表固定字段"')
            insert_values_placeholders.append('?')
            insert_params.append(json.dumps(fixed_json_data, ensure_ascii=False))
            
            # 添加可变字段JSON
            insert_columns.append('"问题表可变字段"')
            insert_values_placeholders.append('?')
            insert_params.append(json.dumps(variable_json_data, ensure_ascii=False))
            
            # 构建并执行SQL
            columns_sql_part = ", ".join(insert_columns)
            placeholders_sql_part = ", ".join(insert_values_placeholders)
            sql_query = f'INSERT INTO Problem ({columns_sql_part}) VALUES ({placeholders_sql_part})'
            
            self.logger.debug(f"Executing SQL for insert: {sql_query} with params: {insert_params}")
            cursor.execute(sql_query, tuple(insert_params))
            new_problem_id = cursor.lastrowid
            conn.commit()

            # 🔧 添加调试日志
            self.logger.debug(f"✓ 成功添加问题记录，新ID: {new_problem_id}")

            return new_problem_id
        except Exception as e:
            self.logger.error(f"Error in add_problem: {str(e)}")
            if conn:
                conn.rollback()
            return None
        finally:
            if conn:
                conn.close()
    
    def update_problem(self, problem_id, data):
        """
        更新问题
        
        参数:
            problem_id: 问题ID
            data: 更新的字段和值
            
        返回:
            是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 1. 获取Problem表的实际物理列名
            cursor.execute("PRAGMA table_info(Problem)")
            actual_table_columns = [info[1] for info in cursor.fetchall()]
            updatable_physical_cols = [col for col in actual_table_columns if col not in ('id', '问题表固定字段', '问题表可变字段')]

            # 2. 获取当前问题在数据库中的JSON字段值
            cursor.execute("SELECT 问题表固定字段, 问题表可变字段 FROM Problem WHERE id = ?", (problem_id,))
            row = cursor.fetchone()
            if not row:
                self.logger.error(f"更新失败：未找到问题ID {problem_id}")
                return False
            
            db_fixed_json_data = json.loads(row[0]) if row[0] and isinstance(row[0], str) else {}
            db_variable_json_data = json.loads(row[1]) if row[1] and isinstance(row[1], str) else {}

            current_fixed_json_data_to_update = db_fixed_json_data.copy()
            current_variable_json_data_to_update = db_variable_json_data.copy()

            set_clauses = []
            sql_params = []
            has_changes = False

            # 3. 处理每个要更新的字段
            self.logger.debug(f"开始处理更新字段，数据: {data}")
            self.logger.debug(f"当前固定字段JSON: {current_fixed_json_data_to_update}")
            self.logger.debug(f"当前可变字段JSON: {current_variable_json_data_to_update}")
            self.logger.debug(f"可更新物理列: {updatable_physical_cols}")

            for field_name, new_value in data.items():
                # 跳过特殊字段
                if field_name in ('id', '问题表固定字段', '问题表可变字段'):
                    continue

                self.logger.debug(f"处理字段: {field_name} = {new_value}")

                # 根据字段名判断应该更新哪个JSON字段
                if field_name in current_fixed_json_data_to_update:
                    # 更新固定字段JSON
                    self.logger.debug(f"字段 {field_name} 在固定字段JSON中，更新值为: {new_value}")
                    current_fixed_json_data_to_update[field_name] = new_value
                    has_changes = True
                elif field_name in current_variable_json_data_to_update:
                    # 更新可变字段JSON
                    self.logger.debug(f"字段 {field_name} 在可变字段JSON中，更新值为: {new_value}")
                    current_variable_json_data_to_update[field_name] = new_value
                    has_changes = True
                elif field_name in updatable_physical_cols:
                    # 更新物理列
                    self.logger.debug(f"字段 {field_name} 是物理列，更新值为: {new_value}")
                    set_clauses.append(f"{field_name} = ?")
                    sql_params.append(new_value)
                    has_changes = True
                else:
                    self.logger.warning(f"字段 {field_name} 不在任何已知字段集合中，跳过更新")

            # 4. 如果有JSON字段的更改，添加到更新语句中
            if has_changes:
                if current_fixed_json_data_to_update != db_fixed_json_data:
                    set_clauses.append("问题表固定字段 = ?")
                    sql_params.append(json.dumps(current_fixed_json_data_to_update, ensure_ascii=False))
                if current_variable_json_data_to_update != db_variable_json_data:
                    set_clauses.append("问题表可变字段 = ?")
                    sql_params.append(json.dumps(current_variable_json_data_to_update, ensure_ascii=False))

            # 5. 执行更新
            if set_clauses:
                sql = f"UPDATE Problem SET {', '.join(set_clauses)} WHERE id = ?"
                sql_params.append(problem_id)
                cursor.execute(sql, sql_params)
                conn.commit()
                # self.logger.info(f"问题 {problem_id} 更新成功")
                return True
            else:
                # self.logger.warning(f"问题 {problem_id} 没有需要更新的字段")
                return True

        except Exception as e:
            self.logger.error(f"更新问题失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def batch_update_problems(self, batch_updates):
        """
        批量更新试验问题记录 - 性能优化版本

        参数:
            batch_updates: 批量更新数据列表，格式为 [{'id': record_id, 'data': update_data}, ...]

        返回:
            int: 成功更新的记录数量
        """
        if not batch_updates:
            return 0

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            success_count = 0

            # 🔧 性能优化：使用事务批量处理
            cursor.execute("BEGIN TRANSACTION")

            try:
                # 1. 获取Problem表的实际物理列名
                cursor.execute("PRAGMA table_info(Problem)")
                actual_table_columns = [info[1] for info in cursor.fetchall()]
                updatable_physical_cols = [col for col in actual_table_columns if col not in ('id', '问题表固定字段', '问题表可变字段')]

                for update_item in batch_updates:
                    problem_id = update_item['id']
                    data = update_item['data'].copy()

                    # 2. 获取当前问题在数据库中的JSON字段值
                    cursor.execute("SELECT 问题表固定字段, 问题表可变字段 FROM Problem WHERE id = ?", (problem_id,))
                    row = cursor.fetchone()
                    if not row:
                        self.logger.error(f"更新失败：未找到问题ID {problem_id}")
                        continue

                    db_fixed_json_str, db_variable_json_str = row

                    # 解析现有JSON数据
                    try:
                        db_fixed_json_data = json.loads(db_fixed_json_str) if db_fixed_json_str else {}
                    except (json.JSONDecodeError, TypeError):
                        db_fixed_json_data = {}

                    try:
                        db_variable_json_data = json.loads(db_variable_json_str) if db_variable_json_str else {}
                    except (json.JSONDecodeError, TypeError):
                        db_variable_json_data = {}

                    # 3. 分类字段并构建更新数据
                    set_clauses = []
                    sql_params = []
                    has_changes = False

                    current_fixed_json_data_to_update = db_fixed_json_data.copy()
                    current_variable_json_data_to_update = db_variable_json_data.copy()

                    for field_name, field_value in data.items():
                        if field_name in updatable_physical_cols:
                            # 物理列直接更新
                            set_clauses.append(f"{field_name} = ?")
                            sql_params.append(field_value)
                            has_changes = True
                        else:
                            # JSON字段更新
                            if field_name in self.fixed_fields:
                                current_fixed_json_data_to_update[field_name] = field_value
                                has_changes = True
                            elif field_name in self.variable_fields:
                                current_variable_json_data_to_update[field_name] = field_value
                                has_changes = True

                    # 4. 如果有JSON字段的更改，添加到更新语句中
                    if has_changes:
                        if current_fixed_json_data_to_update != db_fixed_json_data:
                            set_clauses.append("问题表固定字段 = ?")
                            sql_params.append(json.dumps(current_fixed_json_data_to_update, ensure_ascii=False))
                        if current_variable_json_data_to_update != db_variable_json_data:
                            set_clauses.append("问题表可变字段 = ?")
                            sql_params.append(json.dumps(current_variable_json_data_to_update, ensure_ascii=False))

                    # 5. 执行更新
                    if set_clauses:
                        sql = f"UPDATE Problem SET {', '.join(set_clauses)} WHERE id = ?"
                        sql_params.append(problem_id)
                        cursor.execute(sql, sql_params)

                        if cursor.rowcount > 0:
                            success_count += 1

                # 🔧 提交事务
                cursor.execute("COMMIT")

                self.logger.info(f"批量更新试验问题记录完成，成功更新 {success_count}/{len(batch_updates)} 条记录")
                return success_count

            except Exception as e:
                # 🔧 回滚事务
                cursor.execute("ROLLBACK")
                self.logger.error(f"批量更新试验问题记录事务失败，已回滚: {e}")
                return 0

        except Exception as e:
            self.logger.error(f"批量更新试验问题记录失败: {e}")
            return 0
        finally:
            if conn:
                conn.close()

    def delete_problem(self, problem_id):
        """
        删除问题
        
        参数:
            problem_id: 问题ID
            
        返回:
            是否成功
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM Problem WHERE id = ?", (problem_id,))
            success = cursor.rowcount > 0
            conn.commit()
            
            return success
        except Exception as e:
            self.logger.error(f"删除问题失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()
                
    def get_all_columns(self):
        """
        获取Problem表的所有列名
        
        返回:
            列名列表
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(Problem)")
            columns = [col[1] for col in cursor.fetchall()]
            
            # 获取一行数据以解析JSON字段
            cursor.execute("SELECT 问题表固定字段, 问题表可变字段 FROM Problem LIMIT 1")
            row = cursor.fetchone()
            
            # 如果有数据，解析JSON字段
            if row:
                if row[0]:
                    try:
                        fixed_fields = json.loads(row[0])
                        columns.extend(fixed_fields.keys())
                    except json.JSONDecodeError:
                        pass
                
                if row[1]:
                    try:
                        variable_fields = json.loads(row[1])
                        columns.extend(variable_fields.keys())
                    except json.JSONDecodeError:
                        pass
            
            return columns
        except Exception as e:
            self.logger.error(f"获取列名失败: {e}")
            return []
        finally:
            if conn:
                conn.close()
    
    def sync_field_definitions(self, field_definitions):
        """同步数据库中的字段定义"""
        try:
            # 新增：过滤
            field_definitions = filter_valid_fields(field_definitions)
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取当前字段定义
            cursor.execute("SELECT 问题表固定字段, 问题表可变字段 FROM Problem LIMIT 1")
            row = cursor.fetchone()
            
            # 解析现有字段定义
            fixed_fields = {}
            variable_fields = {}
            if row:
                if row[0]:
                    try:
                        fixed_fields = json.loads(row[0])
                    except json.JSONDecodeError:
                        pass
                if row[1]:
                    try:
                        variable_fields = json.loads(row[1])
                    except json.JSONDecodeError:
                        pass
            
            # 更新字段定义
            for field_name, field_def in field_definitions.items():
                # 根据字段名称判断是固定字段还是可变字段
                if field_name in self.db_derived_fixed_keys:
                    fixed_fields[field_name] = field_def
                else:
                    variable_fields[field_name] = field_def
            
            # 更新数据库
            cursor.execute("""
                UPDATE Problem 
                SET 问题表固定字段 = ?, 问题表可变字段 = ?
                WHERE rowid = 1
            """, (json.dumps(fixed_fields, ensure_ascii=False),
                  json.dumps(variable_fields, ensure_ascii=False)))
            
            conn.commit()
            
        except Exception as e:
            self.logger.error(f"同步字段定义失败: {e}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close() 