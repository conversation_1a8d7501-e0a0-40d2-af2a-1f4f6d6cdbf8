#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
行驶记录表管理器
协调数据模型和UI组件
"""

import os
import json
import logging
from PyQt5.QtCore import QObject, pyqtSignal

from src.models.drive import Drive
from src.views.drive_table_widget import DriveTableWidget

class DriveTableManager(QObject):
    """行驶记录表管理器"""
    
    # 信号定义
    data_changed = pyqtSignal()  # 数据变化信号
    status_message = pyqtSignal(str)  # 状态消息信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据模型
        self.drive_model = Drive()
        
        # 初始化UI组件
        self.drive_widget = None
        
        # 加载字段配置
        self.field_groups = self._load_field_groups()
        
        # 初始化UI组件
        self._init_widget()
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        try:
            # 1. 优先尝试从新系统文件加载（字段导入功能生成的文件）
            config_path = os.path.join("config", "drive_field_groups.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    new_groups = json.load(f)

                # 验证新系统文件是否与实际JSON文件同步
                actual_groups = self._extract_field_groups_from_json_files()
                if self._are_field_groups_consistent(new_groups, actual_groups):
                    self.logger.info("使用新系统字段分组文件（已验证与实际文件一致）")
                    # 转换为旧格式以保持兼容性
                    return self._convert_to_old_format(new_groups)
                else:
                    self.logger.warning("新系统字段分组文件与实际JSON文件不一致，使用实际文件重建")
                    # 更新新系统文件以保持同步
                    try:
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(actual_groups, f, ensure_ascii=False, indent=4)
                        self.logger.info("已更新新系统字段分组文件以保持同步")
                    except Exception as e:
                        self.logger.error(f"更新新系统字段分组文件失败: {e}")
                    return self._convert_to_old_format(actual_groups)

            # 2. 兼容旧的配置文件路径
            old_config_path = os.path.join("config", "行驶记录表字段分组.json")
            if os.path.exists(old_config_path):
                with open(old_config_path, 'r', encoding='utf-8') as f:
                    old_groups = json.load(f)
                    return old_groups

            # 3. 从旧系统文件构建字段分组（兼容性处理）
            actual_groups = self._extract_field_groups_from_json_files()
            if any(actual_groups.values()):
                # 保存为新系统文件以便下次使用
                try:
                    os.makedirs(os.path.dirname(config_path), exist_ok=True)
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(actual_groups, f, ensure_ascii=False, indent=4)
                    self.logger.info(f"从实际JSON文件构建字段分组并保存到 {config_path}")
                except Exception as e:
                    self.logger.error(f"保存字段分组文件失败: {e}")

                return self._convert_to_old_format(actual_groups)

            # 4. 如果都失败了，使用旧的逐个加载方式作为后备
            field_groups = {
                '单独列字段': [],
                '固定字段': [],
                '可变字段': []
            }

            # 加载单独列字段
            individual_path = os.path.join(os.getcwd(), 'config', '行驶记录表单独列字段.json')
            if os.path.exists(individual_path):
                with open(individual_path, 'r', encoding='utf-8') as f:
                    individual_data = json.load(f)
                    if individual_data and len(individual_data) > 0:
                        field_groups['单独列字段'] = list(individual_data[0].keys())

            # 加载固定字段
            fixed_path = os.path.join(os.getcwd(), 'config', '行驶记录表固定字段.json')
            if os.path.exists(fixed_path):
                with open(fixed_path, 'r', encoding='utf-8') as f:
                    fixed_data = json.load(f)
                    if fixed_data and len(fixed_data) > 0:
                        field_groups['固定字段'] = list(fixed_data[0].keys())

            # 加载可变字段
            variable_path = os.path.join(os.getcwd(), 'config', '行驶记录表可变字段.json')
            if os.path.exists(variable_path):
                with open(variable_path, 'r', encoding='utf-8') as f:
                    variable_data = json.load(f)
                    if variable_data and len(variable_data) > 0:
                        field_groups['可变字段'] = list(variable_data[0].keys())

            self.logger.info(f"行驶记录表字段分组配置加载成功")
            return field_groups

        except Exception as e:
            self.logger.error(f"加载行驶记录表字段分组配置失败: {e}")
            return {
                '单独列字段': [],
                '固定字段': [],
                '可变字段': []
            }

    def _extract_field_groups_from_json_files(self):
        """从实际的JSON文件中提取字段分组"""
        field_groups = {"单独列字段": [], "固定字段": [], "可变字段": []}

        # 分别加载三个字段文件并提取字段名
        field_files = [
            ("单独列字段", "行驶记录表单独列字段.json"),
            ("固定字段", "行驶记录表固定字段.json"),
            ("可变字段", "行驶记录表可变字段.json")
        ]

        for field_type, filename in field_files:
            file_path = os.path.join("config", filename)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if data and isinstance(data, list) and len(data) > 0:
                            # 从JSON数组的第一个对象提取字段名
                            field_groups[field_type] = list(data[0].keys())
                            self.logger.info(f"从 {filename} 提取到 {len(field_groups[field_type])} 个字段")
                except Exception as e:
                    self.logger.error(f"读取字段文件 {filename} 失败: {e}")

        return field_groups

    def _are_field_groups_consistent(self, groups1, groups2):
        """检查两个字段分组是否一致"""
        try:
            # 获取所有字段
            fields1 = []
            fields2 = []

            for group_name in ["单独列字段", "固定字段", "可变字段"]:
                fields1.extend(groups1.get(group_name, []))
                fields2.extend(groups2.get(group_name, []))

            # 比较字段集合
            return set(fields1) == set(fields2)
        except Exception as e:
            self.logger.error(f"检查字段分组一致性失败: {e}")
            return False

    def _convert_to_old_format(self, new_groups):
        """将新格式的字段分组转换为统一格式"""
        return {
            '单独列字段': new_groups.get("单独列字段", []),
            '固定字段': new_groups.get("固定字段", []),
            '可变字段': new_groups.get("可变字段", [])
        }

    def _init_widget(self):
        """初始化UI组件"""
        try:
            self.drive_widget = DriveTableWidget()

            # 连接信号
            self.drive_widget.record_added.connect(self._on_record_added)
            self.drive_widget.record_updated.connect(self._on_record_updated)
            self.drive_widget.record_deleted.connect(self._on_record_deleted)
            self.drive_widget.record_selected.connect(self._on_record_selected)

            # 连接公式相关信号
            self.drive_widget.formula_set.connect(self._on_formula_set)

            self.logger.info("行驶记录表UI组件初始化成功")

        except Exception as e:
            self.logger.error(f"初始化行驶记录表UI组件失败: {e}")
    
    def get_widget(self):
        """获取UI组件"""
        return self.drive_widget
    
    def refresh_data(self):
        """刷新数据"""
        try:
            if self.drive_widget:
                self.drive_widget.load_data()
                self.status_message.emit("行驶记录表数据已刷新")
                self.data_changed.emit()
        except Exception as e:
            self.logger.error(f"刷新行驶记录表数据失败: {e}")
            self.status_message.emit("刷新数据失败")
    
    def add_record(self, data=None):
        """添加新记录"""
        try:
            if self.drive_widget:
                if data is None:
                    # 使用默认数据
                    self.drive_widget.add_record()
                else:
                    # 使用提供的数据
                    record_id = self.drive_model.add_record(data, field_groups=self.field_groups)
                    if record_id:
                        self.refresh_data()
                        self.status_message.emit(f"成功添加行驶记录，ID: {record_id}")
                    else:
                        self.status_message.emit("添加行驶记录失败")
        except Exception as e:
            self.logger.error(f"添加行驶记录失败: {e}")
            self.status_message.emit("添加记录失败")

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量
        """
        try:
            if self.drive_widget and hasattr(self.drive_widget, 'add_multiple_records'):
                added_record_ids = self.drive_widget.add_multiple_records(count)
                if added_record_ids:
                    self.status_message.emit(f"成功批量添加 {len(added_record_ids)} 条行驶记录")
                    self.data_changed.emit()
                else:
                    self.status_message.emit("批量添加行驶记录失败")
                return added_record_ids
            else:
                self.status_message.emit("不支持批量添加功能")
                return []
        except Exception as e:
            self.logger.error(f"批量添加行驶记录失败: {e}")
            self.status_message.emit("批量添加记录失败")
            return []

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            if self.drive_widget:
                self.drive_widget.delete_record()
        except Exception as e:
            self.logger.error(f"删除行驶记录失败: {e}")
            self.status_message.emit("删除记录失败")
    
    def export_data(self, file_path=None):
        """导出数据"""
        try:
            if self.drive_widget:
                self.drive_widget.export_data()
        except Exception as e:
            self.logger.error(f"导出行驶记录数据失败: {e}")
            self.status_message.emit("导出数据失败")

    def import_field_json(self, json_data):
        """导入字段JSON配置"""
        try:
            if self.drive_widget:
                self.drive_widget.import_field_json(json_data)
                self.status_message.emit("字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入字段JSON配置失败: {e}")
            self.status_message.emit("导入字段JSON配置失败")

    def update_field_definitions(self, field_definitions):
        """更新字段定义"""
        try:
            if self.drive_widget:
                # 重新加载字段分组配置
                self.field_groups = self._load_field_groups()
                # 更新widget的字段分组
                self.drive_widget.field_groups = self.field_groups

                # 重新加载字段定义配置 - 修复：添加此行以确保字段定义配置文件被重新加载
                self.drive_widget.reload_field_definitions()

                # 重新加载数据以应用新的字段配置
                self.drive_widget.load_data()
                self.status_message.emit("行驶记录表字段定义已更新")
                self.logger.info("行驶记录表字段定义更新成功")
        except Exception as e:
            self.logger.error(f"更新行驶记录表字段定义失败: {e}")
            self.status_message.emit("更新字段定义失败")
    
    def apply_display_settings(self, settings):
        """应用显示设置"""
        try:
            # 保存显示设置到配置文件
            settings_path = os.path.join("config", "行驶记录表显示设置.json")
            os.makedirs(os.path.dirname(settings_path), exist_ok=True)
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            # 刷新UI以应用新设置
            if self.drive_widget:
                self.drive_widget.apply_display_settings(settings)

            self.status_message.emit("行驶记录表显示设置已应用")
            self.logger.info("行驶记录表显示设置应用成功")

        except Exception as e:
            self.logger.error(f"应用行驶记录表显示设置失败: {e}")
            self.status_message.emit("应用显示设置失败")

    def _on_display_settings_changed(self, settings):
        """处理显示设置变更"""
        try:
            self.display_settings = settings
            self.apply_display_settings(settings)
            self.status_message.emit("行驶记录表显示设置已更新")
        except Exception as e:
            self.logger.error(f"更新行驶记录表显示设置失败: {e}")
            self.status_message.emit("更新显示设置失败")
    
    def get_field_groups(self):
        """获取字段分组配置"""
        return self.field_groups
    
    def get_all_fields(self):
        """获取所有字段列表"""
        all_fields = []
        all_fields.extend(self.field_groups.get('单独列字段', []))
        all_fields.extend(self.field_groups.get('固定字段', []))
        all_fields.extend(self.field_groups.get('可变字段', []))
        return all_fields
    
    def get_display_settings(self):
        """获取当前显示设置"""
        try:
            settings_path = os.path.join(os.getcwd(), 'config', '行驶记录表显示设置.json')
            if os.path.exists(settings_path):
                with open(settings_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 返回默认设置，使用新格式(visible_fields)
                all_fields = self.get_all_fields()
                return {
                    'visible_fields': all_fields,
                    'field_order': all_fields
                }
        except Exception as e:
            self.logger.error(f"获取行驶记录表显示设置失败: {e}")
            return {}

    def update_display_settings_with_new_fields(self, new_fields):
        """根据新字段更新显示设置"""
        try:
            # 获取所有字段
            all_fields = self.get_all_fields()

            # 添加新字段到all_fields中（如果不存在）
            for field in new_fields:
                if field not in all_fields:
                    all_fields.append(field)

            # 获取当前显示设置
            config_path = os.path.join("config", "行驶记录表显示设置.json")
            current_settings = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    current_settings = json.load(f)

            # 获取现有的可见字段和字段顺序
            current_visible = set(current_settings.get("visible_fields", []))
            current_order = current_settings.get("field_order", [])
            current_widths = current_settings.get("column_widths", {})

            # 构建新的字段顺序，保持现有字段的顺序，新字段添加到末尾
            new_field_order = []
            new_visible_fields = []

            # 先添加现有顺序中仍然存在的字段
            for field in current_order:
                if field in all_fields:
                    new_field_order.append(field)
                    if field in current_visible:
                        new_visible_fields.append(field)

            # 添加新字段到末尾（默认可见）
            for field in all_fields:
                if field not in new_field_order:
                    new_field_order.append(field)
                    new_visible_fields.append(field)

            # 清理列宽设置，移除不存在的字段
            new_widths = {k: v for k, v in current_widths.items() if k in all_fields}

            # 构建新的显示设置
            new_settings = {
                "visible_fields": new_visible_fields,
                "field_order": new_field_order,
                "column_widths": new_widths
            }

            # 保存更新后的显示设置
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=4)

            # 更新内存中的显示设置
            self.display_settings = new_settings

            self.logger.info("行驶记录表显示设置已根据新字段分组更新")

        except Exception as e:
            self.logger.error(f"更新行驶记录表显示设置失败: {e}")
    
    def _on_record_added(self, record_id):
        """记录添加事件处理"""
        self.logger.info(f"行驶记录已添加，ID: {record_id}")
        self.data_changed.emit()
        self.status_message.emit(f"成功添加行驶记录，ID: {record_id}")
    
    def _on_record_updated(self, record_id):
        """记录更新事件处理"""
        self.logger.debug(f"行驶记录已更新，ID: {record_id}")
        self.data_changed.emit()
        self.status_message.emit(f"行驶记录已更新，ID: {record_id}")

    def _on_record_deleted(self, record_id):
        """记录删除事件处理"""
        self.logger.debug(f"行驶记录已删除，ID: {record_id}")
        self.data_changed.emit()
        self.status_message.emit(f"行驶记录已删除，ID: {record_id}")
    
    def _on_record_selected(self, row):
        """记录选中事件处理"""
        self.logger.debug(f"选中行驶记录，行: {row}")

    def _on_formula_set(self, table_name, column_name, formula):
        """处理公式设置事件"""
        try:
            if table_name != "行驶记录表":
                return

            self.logger.info(f"行驶记录表公式设置: {column_name} = {formula}")

            # 检查是否是新的公式列，如果是则可能需要更新显示设置
            # 获取当前所有字段
            all_fields = self.get_all_fields()

            # 如果公式列不在现有字段中，说明是新添加的列
            if column_name not in all_fields:
                self.logger.info(f"检测到新的公式列: {column_name}")
                # 更新显示设置以包含新的公式列
                self.update_display_settings_with_new_fields([column_name])

                # 刷新数据以应用新的显示设置
                self.refresh_data()

        except Exception as e:
            self.logger.error(f"处理公式设置事件失败: {e}")

    def get_record_count(self):
        """获取记录总数"""
        try:
            df = self.drive_model.get_all_records()
            return len(df)
        except Exception as e:
            self.logger.error(f"获取行驶记录总数失败: {e}")
            return 0
    
    def search_records(self, keyword):
        """搜索记录"""
        try:
            # 这里可以实现搜索功能
            # 暂时返回所有记录
            return self.drive_model.get_all_records()
        except Exception as e:
            self.logger.error(f"搜索行驶记录失败: {e}")
            return []
    
    def filter_records(self, filters):
        """过滤记录"""
        try:
            # 这里可以实现过滤功能
            # 暂时返回所有记录
            return self.drive_model.get_all_records()
        except Exception as e:
            self.logger.error(f"过滤行驶记录失败: {e}")
            return []
