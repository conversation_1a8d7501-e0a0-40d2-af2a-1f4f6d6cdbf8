#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
import os
import datetime
import logging
import json
from src.utils.field_utils import filter_valid_fields

class Scene:
    """典型场景表数据模型类"""
    
    def __init__(self, db_path=None):
        """
        初始化典型场景表模型
        
        参数:
            db_path: 数据库路径
        """
        # 如果未提供数据库路径，使用默认路径
        if db_path is None:
            # 使用当前目录下的数据库文件
            self.db_path = os.path.join(os.getcwd(), '智能驾驶试验管控数据库.db')
        else:
            self.db_path = db_path
            
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库派生的JSON键集合
        self.db_derived_fixed_keys = set()
        self.db_derived_variable_keys = set()
        
        # 延迟测试数据库连接并加载派生键，减少启动时间
        self._connection_tested = False
    
    def _load_derived_json_keys(self, cursor):
        """从数据库加载已存在的固定字段和可变字段JSON字段的键"""
        try:
            cursor.execute("SELECT 典型场景表固定字段, 典型场景表可变字段 FROM scene LIMIT 1")
            row = cursor.fetchone()
            if row:
                if row[0] and isinstance(row[0], str):
                    try:
                        fixed_fields = json.loads(row[0])
                        self.db_derived_fixed_keys.update(fixed_fields.keys())
                    except json.JSONDecodeError:
                        self.logger.warning("无法从数据库解析 '典型场景表固定字段' 以派生键。")
                if row[1] and isinstance(row[1], str):
                    try:
                        variable_fields = json.loads(row[1])
                        self.db_derived_variable_keys.update(variable_fields.keys())
                    except json.JSONDecodeError:
                        self.logger.warning("无法从数据库解析 '典型场景表可变字段' 以派生键。")
            self.logger.info(f"从数据库派生的固定字段JSON键: {self.db_derived_fixed_keys}")
            self.logger.info(f"从数据库派生的可变字段JSON键: {self.db_derived_variable_keys}")
        except Exception as e:
            self.logger.error(f"从数据库加载派生的JSON键失败: {e}")

    def _test_connection_and_load_derived_keys(self):
        """测试数据库连接，检查scene表是否存在，并加载派生JSON键"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查scene表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='scene'")
            if not cursor.fetchone():
                self.logger.error("数据库中不存在scene表")
                raise Exception("数据库中不存在scene表")
                
            # 获取表结构
            cursor.execute("PRAGMA table_info(scene)")
            columns = [col[1] for col in cursor.fetchall()]
            self.logger.info(f"成功连接到数据库，scene表包含以下列: {columns}")

            # 加载派生JSON键
            self._load_derived_json_keys(cursor)
            
        except Exception as e:
            self.logger.error(f"数据库连接测试或派生键加载失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_scene_records(self):
        """
        获取典型场景记录列表

        返回:
            典型场景数据DataFrame
        """
        # 延迟连接测试，只在首次调用时执行
        if not self._connection_tested:
            self._test_connection_and_load_derived_keys()
            self._connection_tested = True
            
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询所有记录
            query = "SELECT * FROM scene ORDER BY id ASC"
            df = pd.read_sql_query(query, conn)
            
            self.logger.info(f"成功获取 {len(df)} 条典型场景记录")
            return df
            
        except Exception as e:
            self.logger.error(f"获取典型场景记录失败: {e}")
            return pd.DataFrame()
        finally:
            if conn:
                conn.close()
    
    def get_all_records(self):
        """
        获取所有典型场景记录（别名方法，与其他模型保持一致）

        返回:
            典型场景数据DataFrame
        """
        return self.get_scene_records()

    def add_record(self, data, field_definitions=None, field_groups=None):
        """
        添加新的典型场景记录

        参数:
            data: 包含记录字段的字典
            field_definitions: 字段定义（用于类型判断）
            field_groups: 字段分组dict，如{"单独列字段": [...], "固定字段": [...], "可变字段": [...]}，用于分流

        返回:
            新记录的ID，如果失败则返回None
        """
        self.logger.debug(f"ENTER add_record with data: {data}")

        # 验证data参数
        if not isinstance(data, dict):
            self.logger.error(f"data参数必须是字典类型，实际为: {type(data)}, 值: {data}")
            return None

        # 清理数据
        if '全不选' in data:
            self.logger.debug("add_record: Removing '全不选' key.")
            del data['全不选']
        if '全选' in data:
            self.logger.debug("add_record: Removing '全选' key.")
            del data['全选']

        self.logger.debug(f"Data after initial cleaning in add_record: {data}")

        # 确保 field_groups 存在
        if not field_groups:
            self.logger.error("field_groups is required for proper field classification")
            return None

        # 调试：打印field_groups的详细信息
        self.logger.debug(f"field_groups type: {type(field_groups)}")
        self.logger.debug(f"field_groups content: {field_groups}")

        # 验证field_groups是字典类型
        if not isinstance(field_groups, dict):
            self.logger.error(f"field_groups应为字典类型，实际为: {type(field_groups)}")
            return None

        # 分离字段
        separate_fields = {}
        fixed_fields = {}
        variable_fields = {}

        # 获取字段分组，确保返回列表类型
        separate_field_names = field_groups.get("单独列字段", [])
        if not isinstance(separate_field_names, list):
            self.logger.warning(f"单独列字段不是列表类型: {type(separate_field_names)}, 使用空列表")
            separate_field_names = []

        fixed_field_names = field_groups.get("固定字段", [])
        if not isinstance(fixed_field_names, list):
            self.logger.warning(f"固定字段不是列表类型: {type(fixed_field_names)}, 使用空列表")
            fixed_field_names = []

        variable_field_names = field_groups.get("可变字段", [])
        if not isinstance(variable_field_names, list):
            self.logger.warning(f"可变字段不是列表类型: {type(variable_field_names)}, 使用空列表")
            variable_field_names = []

        # 分类字段
        for key, value in data.items():
            try:
                if isinstance(separate_field_names, list) and key in separate_field_names:
                    separate_fields[key] = value
                elif isinstance(fixed_field_names, list) and key in fixed_field_names:
                    fixed_fields[key] = value
                elif isinstance(variable_field_names, list) and key in variable_field_names:
                    variable_fields[key] = value
            except Exception as e:
                self.logger.error(f"分类字段时出错，key={key}, separate_field_names类型={type(separate_field_names)}, 错误={e}")
                # 如果出错，跳过这个字段
                continue

        self.logger.debug(f"Separated fields: {separate_fields}")
        self.logger.debug(f"Fixed fields: {fixed_fields}")
        self.logger.debug(f"Variable fields: {variable_fields}")

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建插入语句
            columns = []
            values = []
            placeholders = []

            # 添加单独列字段
            if isinstance(separate_field_names, list):
                for field_name in separate_field_names:
                    if field_name in separate_fields:
                        columns.append(f'"{field_name}"')
                        values.append(separate_fields[field_name])
                        placeholders.append('?')
            else:
                self.logger.warning(f"separate_field_names不是列表，跳过单独列字段处理: {type(separate_field_names)}")

            # 添加JSON字段
            if fixed_fields:
                columns.append('"典型场景表固定字段"')
                values.append(json.dumps(fixed_fields, ensure_ascii=False))
                placeholders.append('?')

            if variable_fields:
                columns.append('"典型场景表可变字段"')
                values.append(json.dumps(variable_fields, ensure_ascii=False))
                placeholders.append('?')

            # 添加修改时间
            columns.append('"修改时间"')
            values.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            placeholders.append('?')

            # 执行插入
            insert_sql = f"INSERT INTO scene ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            self.logger.debug(f"Insert SQL: {insert_sql}")
            self.logger.debug(f"Values: {values}")

            cursor.execute(insert_sql, values)
            record_id = cursor.lastrowid
            conn.commit()

            self.logger.debug(f"成功添加典型场景记录，ID: {record_id}")
            return record_id

        except Exception as e:
            self.logger.error(f"添加典型场景记录失败: {e}")
            if conn:
                conn.rollback()
            return None
        finally:
            if conn:
                conn.close()

    def update_record(self, record_id, data, field_definitions=None, field_groups=None):
        """
        更新典型场景记录

        参数:
            record_id: 记录ID
            data: 包含更新字段的字典
            field_definitions: 字段定义
            field_groups: 字段分组

        返回:
            是否更新成功
        """
        self.logger.debug(f"ENTER update_record with id: {record_id}, data: {data}")

        # 验证data参数
        if not isinstance(data, dict):
            self.logger.error(f"data参数必须是字典类型，实际为: {type(data)}, 值: {data}")
            return False

        # 清理数据
        if '全不选' in data:
            del data['全不选']
        if '全选' in data:
            del data['全选']

        # 确保 field_groups 存在
        if not field_groups:
            self.logger.error("field_groups is required for proper field classification")
            return False

        # 🔧 关键修复：先获取现有的JSON字段数据，避免数据丢失
        existing_fixed_fields = {}
        existing_variable_fields = {}

        # 从数据库读取现有记录的JSON字段
        try:
            conn_read = sqlite3.connect(self.db_path)
            cursor_read = conn_read.cursor()
            cursor_read.execute('SELECT "典型场景表固定字段", "典型场景表可变字段" FROM scene WHERE id = ?', (record_id,))
            existing_record = cursor_read.fetchone()
            conn_read.close()

            if existing_record:
                # 解析现有的固定字段JSON
                if existing_record[0]:
                    try:
                        existing_fixed_fields = json.loads(existing_record[0])
                        self.logger.debug(f"读取到现有固定字段: {existing_fixed_fields}")
                    except json.JSONDecodeError:
                        self.logger.warning(f"无法解析现有固定字段JSON: {existing_record[0]}")

                # 解析现有的可变字段JSON
                if existing_record[1]:
                    try:
                        existing_variable_fields = json.loads(existing_record[1])
                        self.logger.debug(f"读取到现有可变字段: {existing_variable_fields}")
                    except json.JSONDecodeError:
                        self.logger.warning(f"无法解析现有可变字段JSON: {existing_record[1]}")
        except Exception as e:
            self.logger.error(f"读取现有JSON字段失败: {e}")

        # 分离字段
        separate_fields = {}
        fixed_fields = existing_fixed_fields.copy()  # 🔧 从现有数据开始
        variable_fields = existing_variable_fields.copy()  # 🔧 从现有数据开始

        # 获取字段分组，确保返回列表类型
        separate_field_names = field_groups.get("单独列字段", [])
        if not isinstance(separate_field_names, list):
            self.logger.warning(f"单独列字段不是列表类型: {type(separate_field_names)}, 使用空列表")
            separate_field_names = []

        fixed_field_names = field_groups.get("固定字段", [])
        if not isinstance(fixed_field_names, list):
            self.logger.warning(f"固定字段不是列表类型: {type(fixed_field_names)}, 使用空列表")
            fixed_field_names = []

        variable_field_names = field_groups.get("可变字段", [])
        if not isinstance(variable_field_names, list):
            self.logger.warning(f"可变字段不是列表类型: {type(variable_field_names)}, 使用空列表")
            variable_field_names = []

        # 分类字段并合并到现有数据中
        for key, value in data.items():
            try:
                if isinstance(separate_field_names, list) and key in separate_field_names:
                    separate_fields[key] = value
                elif isinstance(fixed_field_names, list) and key in fixed_field_names:
                    fixed_fields[key] = value  # 🔧 更新到现有固定字段中
                    self.logger.debug(f"更新固定字段 {key} = {value}")
                elif isinstance(variable_field_names, list) and key in variable_field_names:
                    variable_fields[key] = value  # 🔧 更新到现有可变字段中
                    self.logger.debug(f"更新可变字段 {key} = {value}")
            except Exception as e:
                self.logger.error(f"分类字段时出错，key={key}, separate_field_names类型={type(separate_field_names)}, 错误={e}")
                # 如果出错，跳过这个字段
                continue

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建更新语句
            set_clauses = []
            values = []

            # 更新单独列字段
            for field_name, value in separate_fields.items():
                set_clauses.append(f'"{field_name}" = ?')
                values.append(value)

            # 🔧 关键修复：始终更新JSON字段以保持数据完整性
            # 即使字段组为空也要更新，确保数据一致性
            set_clauses.append('"典型场景表固定字段" = ?')
            values.append(json.dumps(fixed_fields, ensure_ascii=False))
            self.logger.debug(f"更新固定字段JSON: {fixed_fields}")

            set_clauses.append('"典型场景表可变字段" = ?')
            values.append(json.dumps(variable_fields, ensure_ascii=False))
            self.logger.debug(f"更新可变字段JSON: {variable_fields}")

            # 更新修改时间
            set_clauses.append('"修改时间" = ?')
            values.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

            # 添加WHERE条件
            values.append(record_id)

            # 执行更新
            update_sql = f"UPDATE scene SET {', '.join(set_clauses)} WHERE id = ?"
            self.logger.debug(f"Update SQL: {update_sql}")
            self.logger.debug(f"Values: {values}")

            cursor.execute(update_sql, values)
            conn.commit()

            if cursor.rowcount > 0:
                self.logger.debug(f"成功更新典型场景记录，ID: {record_id}")
                return True
            else:
                self.logger.warning(f"未找到要更新的记录，ID: {record_id}")
                return False

        except Exception as e:
            self.logger.error(f"更新典型场景记录失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def batch_update_scene_records(self, batch_updates, field_definitions=None, field_groups=None):
        """
        批量更新典型场景记录 - 性能优化版本

        参数:
            batch_updates: 批量更新数据列表，格式为 [{'id': record_id, 'data': update_data}, ...]
            field_definitions: 字段定义
            field_groups: 字段分组配置

        返回:
            int: 成功更新的记录数量
        """
        if not batch_updates:
            return 0

        if not field_groups:
            self.logger.error("field_groups is required for proper field classification")
            return 0

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            success_count = 0
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 🔧 性能优化：使用事务批量处理
            cursor.execute("BEGIN TRANSACTION")

            try:
                for update_item in batch_updates:
                    record_id = update_item['id']
                    data = update_item['data'].copy()

                    # 清理数据
                    if '全不选' in data:
                        del data['全不选']
                    if '全选' in data:
                        del data['全选']

                    # 分类字段
                    separate_field_names = field_groups.get('单独列字段', field_groups.get('individual_fields', []))
                    fixed_field_names = field_groups.get('固定字段', field_groups.get('fixed_fields', []))
                    variable_field_names = field_groups.get('可变字段', field_groups.get('variable_fields', []))

                    # 🔧 关键修复：先获取现有的JSON字段数据，避免数据丢失
                    existing_fixed_fields = {}
                    existing_variable_fields = {}

                    # 获取现有记录的JSON字段
                    cursor.execute('SELECT "典型场景表固定字段", "典型场景表可变字段" FROM scene WHERE id = ?', (record_id,))
                    existing_record = cursor.fetchone()

                    if existing_record:
                        if existing_record[0]:  # 固定字段JSON
                            try:
                                existing_fixed_fields = json.loads(existing_record[0])
                            except json.JSONDecodeError:
                                existing_fixed_fields = {}

                        if existing_record[1]:  # 可变字段JSON
                            try:
                                existing_variable_fields = json.loads(existing_record[1])
                            except json.JSONDecodeError:
                                existing_variable_fields = {}

                    # 分类字段并合并到现有数据中
                    separate_fields = {}
                    fixed_fields = existing_fixed_fields.copy()  # 保留现有数据
                    variable_fields = existing_variable_fields.copy()  # 保留现有数据

                    for key, value in data.items():
                        try:
                            if isinstance(separate_field_names, list) and key in separate_field_names:
                                separate_fields[key] = value
                            elif isinstance(fixed_field_names, list) and key in fixed_field_names:
                                fixed_fields[key] = value  # 🔧 更新到现有固定字段中
                            elif isinstance(variable_field_names, list) and key in variable_field_names:
                                variable_fields[key] = value  # 🔧 更新到现有可变字段中
                        except Exception as e:
                            self.logger.error(f"分类字段时出错，key={key}, 错误={e}")
                            continue

                    # 构建更新语句
                    set_clauses = []
                    values = []

                    # 处理单独列字段
                    for field_name, field_value in separate_fields.items():
                        set_clauses.append(f'"{field_name}" = ?')
                        values.append(field_value)

                    # 🔧 关键修复：始终更新JSON字段以保持数据完整性
                    set_clauses.append('"典型场景表固定字段" = ?')
                    values.append(json.dumps(fixed_fields, ensure_ascii=False))

                    set_clauses.append('"典型场景表可变字段" = ?')
                    values.append(json.dumps(variable_fields, ensure_ascii=False))

                    # 更新修改时间
                    set_clauses.append('"修改时间" = ?')
                    values.append(current_time)

                    # 添加WHERE条件
                    values.append(record_id)

                    # 执行更新
                    if set_clauses:
                        update_sql = f"UPDATE scene SET {', '.join(set_clauses)} WHERE id = ?"
                        cursor.execute(update_sql, values)

                        if cursor.rowcount > 0:
                            success_count += 1

                # 🔧 提交事务
                cursor.execute("COMMIT")

                self.logger.info(f"批量更新典型场景记录完成，成功更新 {success_count}/{len(batch_updates)} 条记录")
                return success_count

            except Exception as e:
                # 🔧 回滚事务
                cursor.execute("ROLLBACK")
                self.logger.error(f"批量更新典型场景记录事务失败，已回滚: {e}")
                return 0

        except Exception as e:
            self.logger.error(f"批量更新典型场景记录失败: {e}")
            return 0
        finally:
            if conn:
                conn.close()

    def delete_record(self, record_id):
        """
        删除典型场景记录

        参数:
            record_id: 记录ID

        返回:
            是否删除成功
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("DELETE FROM scene WHERE id = ?", (record_id,))
            conn.commit()

            if cursor.rowcount > 0:
                self.logger.debug(f"成功删除典型场景记录，ID: {record_id}")
                return True
            else:
                self.logger.warning(f"未找到要删除的记录，ID: {record_id}")
                return False

        except Exception as e:
            self.logger.error(f"删除典型场景记录失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def delete_records(self, record_ids):
        """
        批量删除典型场景记录

        参数:
            record_ids: 记录ID列表

        返回:
            删除成功的记录数
        """
        if not record_ids:
            return 0

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建批量删除语句
            placeholders = ','.join(['?'] * len(record_ids))
            delete_sql = f"DELETE FROM scene WHERE id IN ({placeholders})"

            cursor.execute(delete_sql, record_ids)
            conn.commit()

            deleted_count = cursor.rowcount
            self.logger.info(f"成功删除 {deleted_count} 条典型场景记录")
            return deleted_count

        except Exception as e:
            self.logger.error(f"批量删除典型场景记录失败: {e}")
            if conn:
                conn.rollback()
            return 0
        finally:
            if conn:
                conn.close()
